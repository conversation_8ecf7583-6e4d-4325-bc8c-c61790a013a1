import os
import asyncio

def asyncio_run(coro):
    return asyncio.get_event_loop().run_until_complete(coro)

import asyncio
import pytest

from dsl_agent.llm.context_builder import build_system_yaml, build_observation_yaml
from dsl_agent.agent_state import Agent<PERSON>tate
from dsl_agent.assembler.graph_builder import Lang<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def test_build_system_and_observation_yaml():
    # system yaml should be non-empty and contain 'system' key
    sys_yaml = build_system_yaml()
    assert isinstance(sys_yaml, str) and sys_yaml.strip()
    assert "system:" in sys_yaml

    # observation
    state: AgentState = {
        "task": "demo",
        "plan": "x = 1",
        "compilation_error": None,
        "execution_error": None,
        "final_results": {"ok": True},
        "retry_count": 1,
        "inner_graph": None,
        "inner_graph_task_map": None,
    }
    obs_yaml = build_observation_yaml(state)
    assert isinstance(obs_yaml, str) and obs_yaml.strip()
    assert "# Observation (YAML)" in obs_yaml
    assert "passive:" in obs_yaml and "active:" in obs_yaml


def test_exec_trace_with_io_summary(monkeypatch):
    # Minimal graph with one env and one method call
    task_map = {
        "env": {"type": "instantiation", "class_name": "LinuxTerminal", "args": [], "keywords": []},
        "call": {"type": "method_call", "env_var": "env", "method": "run", "args": ["echo hello"], "keywords": []},
    }
    graph = {"env": set(), "call": {"env"}}

    builder = LangGraphBuilder(task_map)
    app = builder.build(graph)

    # enable IO summary
    monkeypatch.setenv("TRACE_INCLUDE_IO", "true")

    initial_state = {"task_results": {}, "live_objects": {}, "exec_trace": []}
    final_state = asyncio_run(app.ainvoke(initial_state))

    assert "exec_trace" in final_state
    assert isinstance(final_state["exec_trace"], list)
    # last event should have io_summary
    assert any("io_summary" in ev for ev in final_state["exec_trace"]) or len(final_state["exec_trace"])>0

