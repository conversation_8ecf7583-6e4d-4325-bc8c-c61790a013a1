import unittest
from unittest.mock import patch, MagicMock
import asyncio
from agent.agent import Agent
from agent.environments.docker_env import DockerEnvironment

class MockDockerEnvironment(DockerEnvironment):
    """Mock DockerEnvironment for testing without actual Docker."""
    async def run(self, command: str):
        if "echo success" in command:
            return "success"
        if "echo step1" in command:
            return "step1_output"
        if "echo step2" in command:
            return "step2_output"
        if "echo Task succeeded!" in command:
            return "Task succeeded!"
        if "echo Task failed!" in command:
            return "Task failed!"
        if "echo fail" in command:
            return "fail"
        if "echo initial" in command:
            return "initial"
        if "echo final" in command:
            return "final"
        return "mock_output"

    async def close(self):
        pass

class TestAgentWorkflowScenarios(unittest.TestCase):
    """Test suite for different DSL branching scenarios."""
    def setUp(self):
        from agent.compiler import ENVIRONMENT_REGISTRY
        self._original_docker_env = ENVIRONMENT_REGISTRY.get("DockerEnvironment")
        ENVIRONMENT_REGISTRY["DockerEnvironment"] = MockDockerEnvironment

    def tearDown(self):
        from agent.compiler import ENVIRONMENT_REGISTRY
        if self._original_docker_env:
            ENVIRONMENT_REGISTRY["DockerEnvironment"] = self._original_docker_env
        else:
            # If it wasn't there to begin with, ensure it's removed
            if "DockerEnvironment" in ENVIRONMENT_REGISTRY:
                del ENVIRONMENT_REGISTRY["DockerEnvironment"]

    async def _run_test_with_plan(self, plan: str, task_description: str):
        """Helper method to run agent with a given plan."""
        with patch('langchain_openai.ChatOpenAI.ainvoke') as mock_ainvoke:
            mock_response = MagicMock()
            mock_response.content = plan
            mock_ainvoke.return_value = mock_response

            agent = Agent(max_retries=1)
            final_results = await agent.run(task_description)
            
            mock_ainvoke.assert_called_once()
            return final_results

    async def test_no_branching(self):
        """Tests a simple sequential plan with no conditional logic."""
        plan = """
env = DockerEnvironment()
result1 = env.run(command="echo step1")
result2 = env.run(command="echo step2")
env.close()
"""
        final_results = await self._run_test_with_plan(plan, "Run a sequence of two steps.")
        
        self.assertEqual(final_results.get("result1"), "step1_output")
        self.assertEqual(final_results.get("result2"), "step2_output")

    async def test_if_branch_true(self):
        """Tests an if branch that is taken."""
        plan = """
env = DockerEnvironment()
status = env.run(command="echo success")
if status == "success":
    print_success = env.run(command="echo Task succeeded!")
env.close()
"""
        final_results = await self._run_test_with_plan(plan, "Run a task that should succeed.")

        self.assertEqual(final_results.get("status"), "success")
        self.assertEqual(final_results.get("print_success"), "Task succeeded!")

    async def test_if_branch_false(self):
        """Tests an if branch that is not taken."""
        plan = """
env = DockerEnvironment()
status = env.run(command="echo fail")
if status == "success":
    print_success = env.run(command="echo Task succeeded!")
env.close()
"""
        final_results = await self._run_test_with_plan(plan, "Run a task that should not execute the success branch.")

        self.assertEqual(final_results.get("status"), "fail")
        self.assertNotIn("print_success", final_results)

    async def test_if_else_branch_if_taken(self):
        """Tests an if/else block where the 'if' part is executed."""
        plan = """
env = DockerEnvironment()
status = env.run(command="echo success")
if status == "success":
    print_success = env.run(command="echo Task succeeded!")
else:
    print_failure = env.run(command="echo Task failed!")
env.close()
"""
        final_results = await self._run_test_with_plan(plan, "Run a task that succeeds and follows the if path.")

        self.assertEqual(final_results.get("status"), "success")
        self.assertEqual(final_results.get("print_success"), "Task succeeded!")
        self.assertNotIn("print_failure", final_results)

    async def test_if_else_branch_else_taken(self):
        """Tests an if/else block where the 'else' part is executed."""
        plan = """
env = DockerEnvironment()
status = env.run(command="echo fail")
if status == "success":
    print_success = env.run(command="echo Task succeeded!")
else:
    print_failure = env.run(command="echo Task failed!")
env.close()
"""
        final_results = await self._run_test_with_plan(plan, "Run a task that fails and follows the else path.")

        self.assertEqual(final_results.get("status"), "fail")
        self.assertNotIn("print_success", final_results)
        self.assertEqual(final_results.get("print_failure"), "Task failed!")

    async def test_while_loop(self):
        """Tests a while loop that executes once."""
        plan = """
env = DockerEnvironment()
status = env.run(command="echo initial")
while status != "final":
    status = env.run(command="echo final")
env.close()
"""
        final_results = await self._run_test_with_plan(plan, "Run a task with a while loop.")

        self.assertEqual(final_results.get("status"), "final")

def load_tests(loader, tests, pattern):
    """Load async tests correctly."""
    suite = unittest.TestSuite()
    for test_class in [TestAgentWorkflowScenarios]:
        tests = loader.loadTestsFromTestCase(test_class)
        for test in tests:
            # This is a bit of a hack to run async tests with the default runner
            # It wraps the async test method in a function that runs the event loop
            test_method = getattr(test, test._testMethodName)
            if asyncio.iscoroutinefunction(test_method):
                setattr(test, test._testMethodName, lambda: asyncio.run(test_method()))
    return tests

if __name__ == '__main__':
    unittest.main()