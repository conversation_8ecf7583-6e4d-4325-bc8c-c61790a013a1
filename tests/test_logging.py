import logging
import json
from dsl_agent.logging_config import configure_logging, get_logger, set_run_id, log_span


def test_plain_logging(caplog):
    caplog.set_level(logging.INFO)
    configure_logging(level="INFO", fmt="plain")
    log = get_logger("t")
    set_run_id("rid-1")
    with log_span(log, "work", user="u1"):
        log.info("hello", extra={"x": 1})
    # Ensure messages captured
    assert any("Start work" in r.message for r in caplog.records)
    assert any("End work" in r.message for r in caplog.records)


def test_json_logging(caplog):
    caplog.set_level(logging.INFO)
    configure_logging(level="INFO", fmt="json")
    log = get_logger("tjson")
    set_run_id("rid-2")
    log.info("hello-json", extra={"a": 2})
    # J<PERSON><PERSON><PERSON>atter writes JSON; caplog stores formatted message in record.message
    assert any("hello-json" in r.message for r in caplog.records)

