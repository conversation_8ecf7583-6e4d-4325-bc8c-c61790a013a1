import unittest
import ast
from collections import defaultdict
from graphlib import TopologicalSorter # Keep for potential use or reference

from dsl_agent.assembler.compiler import Compiler

class TestCompiler(unittest.TestCase):

    def setUp(self):
        self.compiler = Compiler()

    def _get_ready_tasks(self, current_graph: dict) -> set:
        """Helper to find tasks with no remaining dependencies."""
        ready = set()
        for task_name, dependencies in current_graph.items():
            if not dependencies:
                ready.add(task_name)
        return ready

    def _simulate_done(self, current_graph: dict, done_tasks: set):
        """Helper to simulate tasks being done by removing them from dependencies."""
        for task in done_tasks:
            if task in current_graph:
                del current_graph[task] # Remove the task itself
            for node, deps in current_graph.items():
                current_graph[node] = deps - {task} # Remove it from other tasks' dependencies


    def test_simple_dependency(self):
        """Tests a simple plan with one task depending on another."""
        plan = """
a = task_one()
b = task_two(input=a)
"""
        graph, task_map = self.compiler.compile(plan)
        
        # Make a copy of the graph for simulation
        sim_graph = {k: v.copy() for k, v in graph.items()}

        # First batch should be 'a'
        ready1 = self._get_ready_tasks(sim_graph)
        self.assertEqual(ready1, {'a'})
        self._simulate_done(sim_graph, ready1)

        # Second batch should be 'b'
        ready2 = self._get_ready_tasks(sim_graph)
        self.assertEqual(ready2, {'b'})
        self._simulate_done(sim_graph, ready2)

        self.assertFalse(sim_graph) # All tasks should be processed

        # Also test the task_map
        self.assertIn('a', task_map)
        self.assertEqual(task_map['a']['type'], 'tool_call') # Assuming task_one is a tool call
        self.assertEqual(task_map['a']['tool_name'], 'task_one')
        
        self.assertIn('b', task_map)
        self.assertEqual(task_map['b']['type'], 'tool_call')
        self.assertEqual(task_map['b']['tool_name'], 'task_two')
        self.assertIn('a', [kw.value.id for kw in task_map['b']['keywords'] if isinstance(kw.value, ast.Name)] + [arg.id for arg in task_map['b']['args'] if isinstance(arg, ast.Name)])


    def test_parallel_tasks(self):
        """Tests a plan with two independent tasks."""
        plan = """
a = task_one()
b = task_two()
"""
        graph, task_map = self.compiler.compile(plan)
        sim_graph = {k: v.copy() for k, v in graph.items()}
        
        ready_nodes = self._get_ready_tasks(sim_graph)
        self.assertEqual(ready_nodes, {'a', 'b'})
        self._simulate_done(sim_graph, ready_nodes)
        self.assertFalse(sim_graph)

        self.assertIn('a', task_map)
        self.assertEqual(task_map['a']['type'], 'tool_call')
        self.assertEqual(task_map['a']['tool_name'], 'task_one')
        self.assertIn('b', task_map)
        self.assertEqual(task_map['b']['type'], 'tool_call')
        self.assertEqual(task_map['b']['tool_name'], 'task_two')


    def test_multiple_dependencies(self):
        """Tests a task that depends on two other tasks."""
        plan = """
a = task_one()
b = task_two()
c = task_three(in1=a, in2=b)
"""
        graph, task_map = self.compiler.compile(plan)
        sim_graph = {k: v.copy() for k, v in graph.items()}
        
        # First batch should be a and b
        ready_batch1 = self._get_ready_tasks(sim_graph)
        self.assertEqual(ready_batch1, {'a', 'b'})
        self._simulate_done(sim_graph, ready_batch1)
        
        # Second batch should be c
        ready_batch2 = self._get_ready_tasks(sim_graph)
        self.assertEqual(ready_batch2, {'c'})
        self._simulate_done(sim_graph, ready_batch2)
        self.assertFalse(sim_graph)

        self.assertIn('a', task_map)
        self.assertEqual(task_map['a']['type'], 'tool_call')
        self.assertEqual(task_map['a']['tool_name'], 'task_one')
        self.assertIn('b', task_map)
        self.assertEqual(task_map['b']['type'], 'tool_call')
        self.assertEqual(task_map['b']['tool_name'], 'task_two')
        self.assertIn('c', task_map)
        self.assertEqual(task_map['c']['type'], 'tool_call')
        self.assertEqual(task_map['c']['tool_name'], 'task_three')
        self.assertIn('a', [kw.value.id for kw in task_map['c']['keywords'] if isinstance(kw.value, ast.Name)] + [arg.id for arg in task_map['c']['args'] if isinstance(arg, ast.Name)])
        self.assertIn('b', [kw.value.id for kw in task_map['c']['keywords'] if isinstance(kw.value, ast.Name)] + [arg.id for arg in task_map['c']['args'] if isinstance(arg, ast.Name)])


    def test_full_workflow_graph(self):
        """
        Tests the compilation of the full bioinformatics workflow,
        ensuring the dependency graph is correct.
        """
        full_plan = """
ref_genome = download_genome(genome_name="S_cerevisiae")
raw_data = download_sra_data(accession="SRR554369")
indexed_genome = index_genome_bwa(genome_path=ref_genome)
qc_report = run_fastqc(reads_path=raw_data)
aligned_sam = align_reads_bwa(genome_path=indexed_genome, reads_path=raw_data)
"""
        graph, task_map = self.compiler.compile(full_plan)
        sim_graph = {k: v.copy() for k, v in graph.items()}
        
        # First batch: downloads can run in parallel
        ready1 = self._get_ready_tasks(sim_graph)
        self.assertEqual(ready1, {'ref_genome', 'raw_data'})
        self._simulate_done(sim_graph, ready1)
        
        # Second batch: indexing and QC can run in parallel
        ready2 = self._get_ready_tasks(sim_graph)
        self.assertEqual(ready2, {'indexed_genome', 'qc_report'})
        self._simulate_done(sim_graph, ready2)
        
        # Final batch: alignment depends on the previous two
        ready3 = self._get_ready_tasks(sim_graph)
        self.assertEqual(ready3, {'aligned_sam'})
        self._simulate_done(sim_graph, ready3)
        
        self.assertFalse(sim_graph) # All tasks should be processed

        self.assertIn('ref_genome', task_map)
        self.assertEqual(task_map['ref_genome']['type'], 'tool_call')
        self.assertEqual(task_map['ref_genome']['tool_name'], 'download_genome')
        self.assertIn('raw_data', task_map)
        self.assertEqual(task_map['raw_data']['type'], 'tool_call')
        self.assertEqual(task_map['raw_data']['tool_name'], 'download_sra_data')
        self.assertIn('indexed_genome', task_map)
        self.assertEqual(task_map['indexed_genome']['type'], 'tool_call')
        self.assertEqual(task_map['indexed_genome']['tool_name'], 'index_genome_bwa')
        self.assertIn('qc_report', task_map)
        self.assertEqual(task_map['qc_report']['type'], 'tool_call')
        self.assertEqual(task_map['qc_report']['tool_name'], 'run_fastqc')
        self.assertIn('aligned_sam', task_map)
        self.assertEqual(task_map['aligned_sam']['type'], 'tool_call')
        self.assertEqual(task_map['aligned_sam']['tool_name'], 'align_reads_bwa')


    def test_non_assigning_call(self):
        """Tests a plan with a function call that is not assigned to a variable."""
        plan = """
a = task_one()
task_two(input=a)
"""
        graph, task_map = self.compiler.compile(plan)
        sim_graph = {k: v.copy() for k, v in graph.items()}

        # First batch should be 'a'
        ready1 = self._get_ready_tasks(sim_graph)
        self.assertEqual(ready1, {'a'})
        self._simulate_done(sim_graph, ready1)

        # Second batch should be the non-assigned call to task_two
        ready2 = self._get_ready_tasks(sim_graph)
        self.assertEqual(len(ready2), 1)
        task2_name = list(ready2)[0]
        self.assertTrue(task2_name.startswith('expr_'))
        
        # Check that the task map is correct for the generated name
        self.assertIn(task2_name, task_map)
        self.assertEqual(task_map[task2_name]['type'], 'tool_call')
        self.assertEqual(task_map[task2_name]['tool_name'], 'task_two')
        self.assertIn('a', [kw.value.id for kw in task_map[task2_name]['keywords'] if isinstance(kw.value, ast.Name)] + [arg.id for arg in task_map[task2_name]['args'] if isinstance(arg, ast.Name)])

        self._simulate_done(sim_graph, ready2)
        self.assertFalse(sim_graph)
