import asyncio
import pytest
from dsl_agent.executor.executor import Executor

@pytest.mark.asyncio
async def test_executor_run_task_persists(tmp_path, monkeypatch):
    # override session dir
    monkeypatch.setenv("LOG_LEVEL", "ERROR")
    ex = Executor(max_workers=2)
    state = await ex.run_task("echo hi", session_id="sess-1")
    assert state.get("session_id") == "sess-1"
    assert "exec_trace" in state

