import unittest
from unittest.mock import patch, MagicMock, AsyncMock
import asyncio
import docker

from dsl_agent.saver.session_manager import Session<PERSON>anager
from dsl_agent.sessions.session import BashSession

class TestSessionManager(unittest.IsolatedAsyncioTestCase):

    @patch('docker.from_env')
    async def test_create_and_close_session(self, mock_docker_from_env):
        """Tests creating and closing a bash session."""
        mock_docker_client = mock_docker_from_env.return_value
        mock_container = MagicMock()
        mock_container.id = "test_container_id"
        mock_docker_client.containers.run.return_value = mock_container
        mock_docker_client.containers.get.return_value = mock_container # Add this line

        session_manager = SessionManager()
        session_id = await session_manager.create_session('bash')

        self.assertIsNotNone(session_id)
        self.assertIn(session_id, session_manager.sessions)
        self.assertEqual(session_manager.sessions[session_id].tool_type, 'bash')
        self.assertEqual(session_manager.sessions[session_id].container_id, "test_container_id")
        mock_docker_client.containers.run.assert_called_once_with(
            image="compiler-agent-env",
            command="tail -f /dev/null",
            detach=True,
            remove=True,
        )

        await session_manager.close_session(session_id)
        self.assertNotIn(session_id, session_manager.sessions)
        mock_container.stop.assert_called_once()

    @patch('docker.from_env')
    async def test_execute_in_session(self, mock_docker_from_env):
        """Tests executing a command within a session."""
        mock_docker_client = mock_docker_from_env.return_value
        mock_container = MagicMock()
        mock_container.id = "test_container_id"
        mock_docker_client.containers.run.return_value = mock_container
        mock_docker_client.containers.get.return_value = mock_container

        # Mock exec_run to return a successful output
        mock_container.exec_run.return_value = (0, b"command output")

        session_manager = SessionManager()
        session_id = await session_manager.create_session('bash')

        output = await session_manager.execute_in_session(session_id, "ls -l")
        self.assertEqual(output, "command output")
        mock_container.exec_run.assert_called_once_with("ls -l")

        # Test with an error output
        mock_container.exec_run.return_value = (1, b"error output")
        output = await session_manager.execute_in_session(session_id, "bad_command")
        self.assertEqual(output, "error output\nERROR (Exit Code: 1)")

        await session_manager.close_session(session_id)

    @patch('docker.from_env')
    async def test_session_persistence(self, mock_docker_from_env):
        """Tests that state persists between commands in a session."""
        mock_docker_client = mock_docker_from_env.return_value
        mock_container = MagicMock()
        mock_container.id = "test_container_id"
        mock_docker_client.containers.run.return_value = mock_container
        mock_docker_client.containers.get.return_value = mock_container

        session_manager = SessionManager()
        session_id = await session_manager.create_session('bash')

        # First command: create a file
        mock_container.exec_run.return_value = (0, b"")
        await session_manager.execute_in_session(session_id, "touch /tmp/test_file.txt")
        mock_container.exec_run.assert_called_with("touch /tmp/test_file.txt")

        # Second command: list files, should see the created file
        mock_container.exec_run.return_value = (0, b"-rw-r--r-- 1 <USER> <GROUP> 0 Aug 27 12:00 test_file.txt")
        output = await session_manager.execute_in_session(session_id, "ls -l /tmp")
        self.assertIn("test_file.txt", output)
        mock_container.exec_run.assert_called_with("ls -l /tmp")

        await session_manager.close_session(session_id)

    @patch('docker.from_env')
    async def test_unsupported_session_type(self, mock_docker_from_env):
        """Tests creating a session with an unsupported tool type."""
        session_manager = SessionManager()
        with self.assertRaisesRegex(ValueError, "Unsupported session type: ssh"):
            await session_manager.create_session('ssh')

    @patch('docker.from_env')
    async def test_execute_in_non_existent_session(self, mock_docker_from_env):
        """Tests executing in a session that doesn't exist."""
        session_manager = SessionManager()
        with self.assertRaisesRegex(ValueError, "Session non-existent-id not found."):
            await session_manager.execute_in_session("non-existent-id", "ls")

    @patch('docker.from_env')
    async def test_close_non_existent_session(self, mock_docker_from_env):
        """Tests closing a session that doesn't exist (should not error)."""
        session_manager = SessionManager()
        # Should not raise an error
        await session_manager.close_session("non-existent-id")

if __name__ == '__main__':
    unittest.main()
