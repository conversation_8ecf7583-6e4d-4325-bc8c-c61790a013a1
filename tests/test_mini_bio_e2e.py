import os
import pytest

from dsl_agent.assembler.graph_builder import LangGraphBuilder
from dsl_agent.tool_manager import ENVIRONMENT_REGISTRY


def build_min_graph():
    # Minimal chain: instantiate FastQCEnv and run a cheap command
    task_map = {
        "qc": {"type": "instantiation", "class_name": "LinuxTerminal", "args": [], "keywords": []},
        "echo": {"type": "method_call", "env_var": "qc", "method": "run", "args": [], "keywords": []},
    }
    graph = {"qc": set(), "echo": {"qc"}}
    app = LangGraphBuilder(task_map).build(graph)
    return app


@pytest.mark.skip(reason="Mini E2E placeholder; real data flow requires docker and test datasets")
def test_mini_bio_e2e():
    app = build_min_graph()
    state = {"task_results": {}, "live_objects": {}, "exec_trace": []}
    out = pytest.asyncio.run(app.ainvoke(state))
    assert "exec_trace" in out

