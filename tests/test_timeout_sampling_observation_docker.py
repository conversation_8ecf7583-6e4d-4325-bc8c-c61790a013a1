import os
import asyncio
import pytest

from dsl_agent.compiler.base import env_tool
from dsl_agent.tool_manager import ENVIRONMENT_REGISTRY
from dsl_agent.assembler.graph_builder import LangGraphBuilder
from dsl_agent.llm.context_builder import build_observation_yaml


# Helpers
class TestEnv:
    def __init__(self):
        pass

    @env_tool
    async def slow(self, seconds: float) -> str:
        await asyncio.sleep(seconds)
        return "done"

    @env_tool
    async def quick(self) -> str:
        return "ok"


def run_async(coro):
    return asyncio.get_event_loop().run_until_complete(coro)


@pytest.fixture(autouse=True)
def restore_env(monkeypatch):
    # Default cleanup for env vars touched in tests
    for k in [
        "NODE_TIMEOUT_SEC",
        "NODE_RETRIES",
        "NODE_RETRY_BACKOFF_MS",
        "TRACE_SAMPLE_RATE",
        "OBS_YAML_SOFT_LIMIT_BYTES",
    ]:
        monkeypatch.delenv(k, raising=False)
    yield


def _build_app_for_env(env_cls, method_name: str, args=None):
    import ast
    py_args = args or []
    # Convert python literals to AST constants for the graph builder
    ast_args = [ast.Constant(value=a) for a in py_args]
    # Temporarily register test env
    ENVIRONMENT_REGISTRY["TestEnv"] = env_cls
    task_map = {
        "e": {"type": "instantiation", "class_name": "TestEnv", "args": [], "keywords": []},
        "call": {"type": "method_call", "env_var": "e", "method": method_name, "args": ast_args, "keywords": []},
    }
    graph = {"e": set(), "call": {"e"}}
    builder = LangGraphBuilder(task_map)
    return builder.build(graph)


def test_timeout_and_retry(monkeypatch):
    # Configure short timeout, no retries
    monkeypatch.setenv("NODE_TIMEOUT_SEC", "0.1")
    monkeypatch.setenv("NODE_RETRIES", "0")
    app = _build_app_for_env(TestEnv, "slow", [0.2])
    final_state = run_async(app.ainvoke({"task_results": {}, "live_objects": {}, "exec_trace": []}))

    # Expect an error event with TimeoutError
    evs = final_state.get("exec_trace", [])
    assert any(ev.get("error_type") == "TimeoutError" for ev in evs), evs


def test_trace_sampling(monkeypatch):
    # With sample rate 1.0 expect both instantiation + method_call events
    monkeypatch.setenv("TRACE_SAMPLE_RATE", "1.0")
    app = _build_app_for_env(TestEnv, "quick")
    s1 = run_async(app.ainvoke({"task_results": {}, "live_objects": {}, "exec_trace": []}))
    evs1 = s1.get("exec_trace", [])
    assert len(evs1) >= 2  # instantiation + method_call

    # With sample rate 0.0, method_call event should be dropped (instantiation remains)
    monkeypatch.setenv("TRACE_SAMPLE_RATE", "0.0")
    app2 = _build_app_for_env(TestEnv, "quick")
    s2 = run_async(app2.ainvoke({"task_results": {}, "live_objects": {}, "exec_trace": []}))
    evs2 = s2.get("exec_trace", [])
    assert len(evs2) <= 1, evs2


def test_observation_soft_budget(monkeypatch):
    # Force very small budget
    monkeypatch.setenv("OBS_YAML_SOFT_LIMIT_BYTES", "200")
    # Build a huge plan to exceed the budget
    state = {
        "task": "demo",
        "plan": "X" * 10000,
        "compilation_error": None,
        "execution_error": None,
        "final_results": {"big": "Y" * 10000},
        "retry_count": 0,
    }
    y = build_observation_yaml(state)
    assert "# <truncated: soft limit reached>" in y


@pytest.mark.skipif(not os.getenv("DOCKER_TEST_IMAGE"), reason="No DOCKER_TEST_IMAGE set; skipping docker test")
def test_dockerterminal_error_and_shell(monkeypatch):
    from dsl_agent.environments.docker_terminal import DockerTerminal

    image = os.getenv("DOCKER_TEST_IMAGE")
    env = DockerTerminal(image=image)

    # stderr + non-zero exit
    out = run_async(env.run("ls /nonexistent"))
    assert "ERROR (Exit Code:" in out
    assert "STDERR:" in out or "No such file" in out

    # Pipeline (shell semantics via /bin/sh -lc)
    out2 = run_async(env.run("printf 'hi' | tr a-z A-Z"))
    assert "HI" in out2

    run_async(env.close())

