from dsl_agent.llm.manager import create_model
from dsl_agent.llm.context_builder import build_system_yaml

def test_llm_manager_create_model():
    m = create_model("gemini-2.5-flash")
    # At least ensure it has invoke/ainvoke methods typical for ChatOpenAI
    assert hasattr(m, "invoke") or hasattr(m, "ainvoke")


def test_context_builder_schema_only():
    s = build_system_yaml()
    # Should not contain directive words from old prompt manager
    assert "Use only the provided DSL environments" not in s
    # Should include class names from registry, e.g., DockerTerminal
    assert "class DockerTerminal:" in s or "class LinuxTerminal:" in s

