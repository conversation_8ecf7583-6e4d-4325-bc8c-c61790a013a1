import unittest
import async<PERSON>
from dsl_agent.assembler.compiler import Compiler
from dsl_agent.assembler.graph_builder import LangGraphBuilder


class TestConditionalBranches(unittest.TestCase):
    """Test cases for if/else conditional branch functionality."""

    def setUp(self):
        self.compiler = Compiler()

    def test_simple_if_else_compilation(self):
        """Test that if/else statements are compiled correctly."""
        plan = """
env = DockerEnvironment()
status = env.run(command="echo success")

if status == "success":
    success_msg = env.run(command="echo Task succeeded!")
else:
    failure_msg = env.run(command="echo Task failed!")

env.close()
"""
        graph, task_map = self.compiler.compile(plan)
        
        # Check that all expected nodes are present in the main task_map
        expected_top_level_nodes = ['env', 'status']
        for node in expected_top_level_nodes:
            self.assertIn(node, task_map)

        # Check that conditional node is created
        conditional_nodes = [name for name, info in task_map.items() 
                           if info.get("type") == "conditional_branch"]
        self.assertEqual(len(conditional_nodes), 1)
        
        conditional_node = conditional_nodes[0]
        conditional_info = task_map[conditional_node]
        
        # Verify conditional node properties
        self.assertEqual(conditional_info["condition_var"], "status")
        self.assertEqual(conditional_info["operator"], "==")
        self.assertEqual(conditional_info["value"], "success")

        # Get subgraph names
        if_subgraph_name = conditional_info["if_branch_start"]
        else_subgraph_name = conditional_info["else_branch_start"]

        self.assertIsNotNone(if_subgraph_name)
        self.assertIsNotNone(else_subgraph_name)

        # Check if_subgraph
        self.assertIn(if_subgraph_name, task_map)
        self.assertEqual(task_map[if_subgraph_name]['type'], 'subgraph')
        self.assertIn('success_msg', task_map[if_subgraph_name]['task_map'])
        self.assertEqual(task_map[if_subgraph_name]['task_map']['success_msg']['type'], 'method_call')

        # Check else_subgraph
        self.assertIn(else_subgraph_name, task_map)
        self.assertEqual(task_map[else_subgraph_name]['type'], 'subgraph')
        self.assertIn('failure_msg', task_map[else_subgraph_name]['task_map'])
        self.assertEqual(task_map[else_subgraph_name]['task_map']['failure_msg']['type'], 'method_call')

    async def test_if_else_execution_success_branch(self):
        """Test that only the success branch executes when condition is true."""
        plan = """
env = DockerEnvironment()
status = env.run(command="echo success")

if status == "success":
    success_msg = env.run(command="echo Task succeeded!")
else:
    failure_msg = env.run(command="echo Task failed!")

env.close()
"""
        graph, task_map = self.compiler.compile(plan)
        builder = LangGraphBuilder(task_map)
        app = builder.build(graph)
        
        # Initial state for the graph
        initial_state = {"task_results": {}, "live_objects": {}}
        
        # Execute the graph
        final_state = await app.ainvoke(initial_state)
        
        # Check results
        results = final_state["task_results"]
        
        # Status should be "success"
        self.assertEqual(results["status"], "success")
        
        # Success message should be executed
        self.assertIn("success_msg", results)
        self.assertEqual(results["success_msg"], "Task succeeded!")
        
        # Failure message should NOT be executed
        self.assertNotIn("failure_msg", results)

    async def test_if_else_execution_failure_branch(self):
        """Test that only the failure branch executes when condition is false."""
        plan = """
env = DockerEnvironment()
status = env.run(command="echo failure")

if status == "success":
    success_msg = env.run(command="echo Task succeeded!")
else:
    failure_msg = env.run(command="echo Task failed!")

env.close()
"""
        graph, task_map = self.compiler.compile(plan)
        builder = LangGraphBuilder(task_map)
        app = builder.build(graph)
        
        # Initial state for the graph
        initial_state = {"task_results": {}, "live_objects": {}}
        
        # Execute the graph
        final_state = await app.ainvoke(initial_state)
        
        # Check results
        results = final_state["task_results"]
        
        # Status should be "failure"
        self.assertEqual(results["status"], "failure")
        
        # Failure message should be executed
        self.assertIn("failure_msg", results)
        self.assertEqual(results["failure_msg"], "Task failed!")
        
        # Success message should NOT be executed
        self.assertNotIn("success_msg", results)

    async def test_different_operators(self):
        """Test different comparison operators in if statements."""
        plan = """
env = DockerEnvironment()
number = env.run(command="echo 5")

if number != "3":
    not_equal_msg = env.run(command="echo Not equal to 3")
else:
    equal_msg = env.run(command="echo Equal to 3")

env.close()
"""
        graph, task_map = self.compiler.compile(plan)
        builder = LangGraphBuilder(task_map)
        app = builder.build(graph)
        
        # Initial state for the graph
        initial_state = {"task_results": {}, "live_objects": {}}
        
        # Execute the graph
        final_state = await app.ainvoke(initial_state)
        
        # Check results
        results = final_state["task_results"]
        
        # Number should be "5"
        self.assertEqual(results["number"], "5")
        
        # Not equal message should be executed (5 != 3)
        self.assertIn("not_equal_msg", results)
        self.assertEqual(results["not_equal_msg"], "Not equal to 3")
        
        # Equal message should NOT be executed
        self.assertNotIn("equal_msg", results)


def run_async_test(test_func):
    """Helper function to run async tests."""
    return asyncio.run(test_func())


if __name__ == '__main__':
    # Create a custom test suite that can handle async tests
    suite = unittest.TestSuite()
    
    # Add sync tests
    suite.addTest(TestConditionalBranches('test_simple_if_else_compilation'))
    
    # Add async tests (we'll need to run these separately)
    async_tests = [
        'test_if_else_execution_success_branch',
        'test_if_else_execution_failure_branch', 
        'test_different_operators'
    ]
    
    runner = unittest.TextTestRunner(verbosity=2)
    
    # Run sync tests
    print("Running synchronous tests...")
    result = runner.run(suite)
    
    # Run async tests
    print("\nRunning asynchronous tests...")
    test_instance = TestConditionalBranches()
    test_instance.setUp()
    
    for test_name in async_tests:
        print(f"\nRunning {test_name}...")
        try:
            test_method = getattr(test_instance, test_name)
            asyncio.run(test_method())
            print(f"✓ {test_name} PASSED")
        except Exception as e:
            print(f"✗ {test_name} FAILED: {e}")
