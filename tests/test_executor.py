import os
import unittest
from unittest.mock import patch, MagicMock, AsyncMock, call
from pathlib import Path

from dsl_agent.assembler.compiler import Compiler
from dsl_agent.executor.executor import Executor

class TestExecutor(unittest.IsolatedAsyncioTestCase):

    @patch('agent.executor.executor.TOOL_REGISTRY')
    @patch('agent.executor.executor.SessionManager')
    @patch('docker.from_env')
    async def test_execution_order_and_argument_passing(self, mock_docker_from_env, mock_session_manager_class, mock_tool_registry):
        """
        Tests that a simple dependent workflow executes in the correct order
        and that file paths are passed correctly between tasks.
        """
        # 1. Setup Mocks
        # Mock Docker client to avoid real container operations
        mock_docker_client = mock_docker_from_env.return_value
        mock_docker_client.containers.run.return_value = b"docker log"

        # Mock tool functions
        mock_task_one = MagicMock(return_value={"command": "cmd1", "output_path": "./out1.txt"})
        mock_task_two = MagicMock(return_value={"command": "cmd2", "output_path": "./out2.txt"})

        # Configure TOOL_REGISTRY mock
        mock_tool_registry.get.side_effect = lambda key: {"task_one": mock_task_one, "task_two": mock_task_two}.get(key)
        mock_tool_registry.__getitem__.side_effect = lambda key: {"task_one": mock_task_one, "task_two": mock_task_two}[key]

        # Mock SessionManager
        mock_session_manager = MagicMock()
        mock_session_manager_class.return_value = mock_session_manager

        # Create real executor instance
        executor = Executor()

        # 2. Define plan and compile
        plan = """
a = task_one()
b = task_two(input_path=a)
"""
        compiler = Compiler()
        sorter = compiler.compile(plan)

        # 3. Execute
        results = await executor.execute(sorter, compiler)

        # 4. Assertions
        # Assert that the tools were called with the correct arguments
        mock_task_one.assert_called_once_with()
        mock_task_two.assert_called_once_with(input_path="/data/out1.txt")

        # Assert final results dictionary is correct
        self.assertTrue(results['a'].endswith("data/out1.txt"))
        self.assertTrue(results['b'].endswith("data/out2.txt"))

    @patch('agent.executor.executor.TOOL_REGISTRY')
    @patch('agent.executor.executor.SessionManager')
    @patch('docker.from_env')
    async def test_parallel_execution(self, mock_docker_from_env, mock_session_manager_class, mock_tool_registry):
        """Tests that independent tasks are executed in parallel."""
        # 1. Setup Mocks
        mock_docker_client = mock_docker_from_env.return_value
        mock_docker_client.containers.run.return_value = b"docker log"

        mock_task_one = MagicMock(return_value={"command": "cmd1", "output_path": "./out1.txt"})
        mock_task_two = MagicMock(return_value={"command": "cmd2", "output_path": "./out2.txt"})
        mock_task_three = MagicMock(return_value={"command": "cmd3", "output_path": "./out3.txt"})

        # Configure TOOL_REGISTRY mock
        mock_tool_registry.get.side_effect = lambda key: {
            "task_one": mock_task_one,
            "task_two": mock_task_two,
            "task_three": mock_task_three
        }.get(key)
        mock_tool_registry.__getitem__.side_effect = lambda key: {
            "task_one": mock_task_one,
            "task_two": mock_task_two,
            "task_three": mock_task_three
        }[key]

        # Mock SessionManager
        mock_session_manager = MagicMock()
        mock_session_manager_class.return_value = mock_session_manager

        # Create real executor instance
        executor = Executor()

        # 2. Define plan and compile
        plan = """
a = task_one()
b = task_two()
c = task_three(in1=a, in2=b)
"""
        compiler = Compiler()
        sorter = compiler.compile(plan)

        # 3. Execute
        await executor.execute(sorter, compiler)

        # 4. Assertions
        # Assert that the tools were called with the correct arguments
        mock_task_one.assert_called_once_with()
        mock_task_two.assert_called_once_with()
        mock_task_three.assert_called_once_with(in1="/data/out1.txt", in2="/data/out2.txt")

    @patch('agent.executor.executor.TOOL_REGISTRY')
    @patch('agent.executor.executor.SessionManager')
    @patch('docker.from_env')
    async def test_tool_not_found(self, mock_docker_from_env, mock_session_manager_class, mock_tool_registry):
        """Tests that a ValueError is raised for an unregistered tool."""
        # Configure TOOL_REGISTRY mock to return None for unregistered tools
        mock_tool_registry.get.return_value = None

        # Mock SessionManager
        mock_session_manager = MagicMock()
        mock_session_manager_class.return_value = mock_session_manager

        # Create real executor instance
        executor = Executor()

        plan = "a = unregistered_tool()"
        compiler = Compiler()
        sorter = compiler.compile(plan)

        with self.assertRaisesRegex(ValueError, "Tool 'unregistered_tool' not found in registry"):
            await executor.execute(sorter, compiler)

    @patch('agent.executor.executor.TOOL_REGISTRY')
    @patch('agent.executor.executor.SessionManager')
    @patch('docker.from_env')
    async def test_stateful_bash_workflow(self, mock_docker_from_env, mock_session_manager_class, mock_tool_registry):
        """Tests the execution of a stateful bash workflow."""
        # 1. Setup Mocks
        mock_docker_client = mock_docker_from_env.return_value
        mock_docker_client.containers.run.return_value = b"docker log"

        # Mock SessionManager
        mock_session_manager = MagicMock()
        mock_session_manager.create_session = AsyncMock(return_value="test_session_id")
        mock_session_manager.execute_in_session = AsyncMock()
        mock_session_manager.execute_in_session.side_effect = [
            ("", "", 0),  # Output for touch command (stdout, stderr, exit_code)
            ("total 0\n-rw-r--r-- 1 <USER> <GROUP> 0 Aug 27 12:00 session_file.txt", "", 0)  # Output for ls command
        ]
        mock_session_manager.close_session = AsyncMock(return_value=None)
        mock_session_manager.sessions = {}
        mock_session_manager_class.return_value = mock_session_manager

        # Configure TOOL_REGISTRY mock for stateful tools
        mock_tool_registry.get.side_effect = lambda key: {
            "bash_start": MagicMock(),
            "bash_run": MagicMock(),
            "bash_stop": MagicMock(),
        }.get(key)

        # Create real executor instance
        executor = Executor()

        # 2. Define plan and compile
        plan = """
s = bash_start()
create_file_output = bash_run(session=s, command="touch /data/session_file.txt")
list_files_output = bash_run(session=s, command="ls -l /data")
bash_stop(session=s)
"""
        compiler = Compiler()
        sorter = compiler.compile(plan)

        # 3. Execute
        results = await executor.execute(sorter, compiler)

        # 4. Assertions
        mock_session_manager.create_session.assert_called_once_with('bash')
        mock_session_manager.execute_in_session.assert_has_calls([
            call("test_session_id", "touch /data/session_file.txt"),
            call("test_session_id", "ls -l /data"),
        ])
        mock_session_manager.close_session.assert_called_once_with("test_session_id")

        self.assertEqual(results['s'], "test_session_id")
        self.assertEqual(results['create_file_output'], ("", "", 0))
        self.assertEqual(results['list_files_output'], ("total 0\n-rw-r--r-- 1 <USER> <GROUP> 0 Aug 27 12:00 session_file.txt", "", 0))
        # bash_stop is a non-assigning call, so it gets an auto-generated name
        bash_stop_key = [k for k in results.keys() if k.startswith('expr_')][0]
        self.assertIsNone(results[bash_stop_key])

if __name__ == '__main__':
    unittest.main()
