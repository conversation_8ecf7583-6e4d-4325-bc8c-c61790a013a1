import os
from typing import Any
from dsl_agent.logging_config import get_logger

# Support both class-based and contextmanager-based APIs across versions
try:
    from langgraph.checkpoint.redis import RedisSaver  # type: ignore
except Exception:
    RedisSaver = None  # type: ignore

try:
    from langgraph.checkpoint.redis import redis_saver  # type: ignore
except Exception:
    redis_saver = None  # type: ignore

try:
    from langgraph.checkpoint.memory import MemorySaver  # type: ignore
except Exception:
    MemorySaver = None  # type: ignore

_ctx: Any = None


def get_redis_saver():  # returns a saver instance suitable for compile(checkpointer=...)
    if os.getenv("REDIS_DISABLE", "false").lower() in {"1", "true", "yes"}:
        if MemorySaver is not None:
            get_logger(__name__).warning("Redis disabled by env; using MemorySaver")
            return MemorySaver()
    """
    Create or acquire a RedisSaver from env config.
    Env:
      - REDIS_URL (preferred), e.g. redis://localhost:6379/0
      - or REDIS_HOST, REDIS_PORT, REDIS_DB
    """
    log = get_logger(__name__)

    url = os.getenv("REDIS_URL")
    if not url:
        host = os.getenv("REDIS_HOST", "127.0.0.1")
        port = int(os.getenv("REDIS_PORT", "6379"))
        db = int(os.getenv("REDIS_DB", "0"))
        url = f"redis://{host}:{port}/{db}"

    log.info("Using RedisSaver", extra={"url": url})

    # Prefer class API; validate interface
    if RedisSaver is not None and hasattr(RedisSaver, "from_conn_string"):
        saver = RedisSaver.from_conn_string(url)  # type: ignore
        if hasattr(saver, "get_next_version") and hasattr(saver, "put"):
            return saver
        get_logger(__name__).warning("RedisSaver created but missing required methods; falling back")

    # Fallback to contextmanager API (best-effort)
    if redis_saver is not None:
        global _ctx
        _ctx = redis_saver(url)
        saver = _ctx.__enter__()
        if hasattr(saver, "get_next_version") and hasattr(saver, "put"):
            return saver
        get_logger(__name__).warning("redis_saver returned incompatible saver; falling back to MemorySaver")

    # Final fallback to MemorySaver
    if MemorySaver is not None:
        return MemorySaver()
    raise RuntimeError("No compatible Redis saver API found and MemorySaver unavailable")


