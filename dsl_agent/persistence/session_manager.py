import json
from pathlib import Path
from typing import Optional, Dict, Any
from threading import Lock

SESS_DIR = Path("data/sessions")
SESS_DIR.mkdir(parents=True, exist_ok=True)


class SessionStore:
    """
    Minimal JSON file-based session persistence.
    - Saves latest AgentState per session_id
    - Thread-safe file writes via a process-local Lock
    """
    def __init__(self):
        self._lock = Lock()

    def _path(self, session_id: str) -> Path:
        return SESS_DIR / f"{session_id}.json"

    def save(self, session_id: str, state: Dict[str, Any]) -> None:
        p = self._path(session_id)
        with self._lock:
            p.write_text(json.dumps(state, ensure_ascii=False, indent=2), encoding="utf-8")

    def load(self, session_id: str) -> Optional[Dict[str, Any]]:
        p = self._path(session_id)
        if not p.exists():
            return None
        try:
            return json.loads(p.read_text(encoding="utf-8"))
        except Exception:
            return None

    def update(self, session_id: str, patch: Dict[str, Any]) -> Dict[str, Any]:
        current = self.load(session_id) or {}
        current.update(patch)
        self.save(session_id, current)
        return current

