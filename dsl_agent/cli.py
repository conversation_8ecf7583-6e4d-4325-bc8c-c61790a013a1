import asyncio
import os
from typing import Dict, Any, List, Optional

from prompt_toolkit import PromptSession
from prompt_toolkit.key_binding import KeyBindings
from rich.panel import Panel
from rich.table import Table
from rich.console import Console
from rich.live import Live
from rich.layout import Layout
from rich.text import Text
from rich.box import SQUARE

from dsl_agent.agent import Agent

STATUS_COLORS = {
    "pending": "grey70",
    "running": "yellow",
    "ok": "green",
    "error": "red",
}


def _render_dag_panel(nodes: List[str], edges: Dict[str, List[str]], status: Dict[str, str]) -> Panel:
    table = Table(expand=True, show_edge=False, box=SQUARE)
    table.add_column("Node", style="bold")
    table.add_column("Depends")
    table.add_column("Status")
    for n in nodes:
        deps = ", ".join(edges.get(n, []))
        st = status.get(n, "pending")
        table.add_row(Text(n, style=STATUS_COLORS.get(st, "white")), deps, Text(st, style=STATUS_COLORS.get(st, "white")))
    return Panel(table, title="DAG", border_style="white")


def _render_messages_panel(messages: List[str]) -> Panel:
    txt = "\n".join(messages[-20:]) if messages else "<no messages>"
    return Panel(Text(txt), title="Conversation", border_style="white", box=SQUARE)


def _render_logs_panel(logs: List[str]) -> Panel:
    txt = "\n".join(logs[-100:]) if logs else "<no logs>"
    return Panel(Text(txt), title="Logs", border_style="white", box=SQUARE)


async def _stream_run(agent: Agent, task: str, session_id: Optional[str], console: Console, show_dag: bool, show_log: bool) -> None:
    layout = Layout()

    # Dynamic 3-panel layout (Conversation | DAG | Logs), DAG/Logs can be hidden
    if show_dag and show_log:
        layout.split_row(
            Layout(name="conv", ratio=3),
            Layout(name="dag", ratio=2),
            Layout(name="logs", ratio=2),
        )
    elif show_dag and not show_log:
        layout.split_row(
            Layout(name="conv", ratio=3),
            Layout(name="dag", ratio=2),
        )
    elif (not show_dag) and show_log:
        layout.split_row(
            Layout(name="conv", ratio=3),
            Layout(name="logs", ratio=2),
        )
    else:
        layout.split_row(Layout(name="conv", ratio=1))

    nodes: List[str] = []
    edges: Dict[str, List[str]] = {}
    status: Dict[str, str] = {}
    messages: List[str] = []
    logs: List[str] = []

    async def render():
        # Conversation always present
        layout["conv"].update(_render_messages_panel(messages))
        # Optional panels
        if "dag" in layout.children_map:
            layout["dag"].update(_render_dag_panel(nodes, edges, status))
        if "logs" in layout.children_map:
            layout["logs"].update(_render_logs_panel(logs))
        return layout

    # Build initial state and stream events
    initial_state = {
        "task": task,
        "plan": None,
        "compilation_error": None,
        "execution_error": None,
        "final_results": None,
        "retry_count": 0,
        "messages": [],
        "session_id": session_id,
        "inner_graph": None,
        "inner_graph_task_map": None,
    }

    thread_id = session_id or os.getenv("RUN_ID") or "cli"

    async def handle_event(event: Dict[str, Any]):
        ev = event.get("event")
        name = event.get("name")
        data = event.get("data") or {}
        logs.append(f"{ev or '?'} {name or ''}")

        # Build DAG on compile end
        if name == "compile_step" and ev in {"on_end", "end"}:
            out = data.get("output") or {}
            graph = out.get("inner_graph") or {}
            nodes.clear(); edges.clear(); status.clear()
            for n, deps in graph.items():
                nodes.append(n)
                edges[n] = list(deps) if isinstance(deps, (list, set)) else list(deps or [])
                status[n] = "pending"
            messages.append("[system] DAG compiled: " + ", ".join(nodes))

        # Node status updates
        if name and name not in {"plan_step", "compile_step", "execute_step"}:
            if ev in {"on_start", "start"}:
                status[name] = "running"
            elif ev in {"on_end", "end"}:
                out = (data or {}).get("output") or {}
                err = out.get("execution_error") or out.get("compilation_error")
                status[name] = "error" if err else "ok"

        # Append messages
        out_msgs = (data or {}).get("output", {}).get("messages")
        if isinstance(out_msgs, list):
            messages.extend([str(m) for m in out_msgs])

    console.clear()
    with Live(await render(), console=console, refresh_per_second=10, screen=True) as live:
        async for event in agent.app.astream_events(initial_state, config={"configurable": {"thread_id": thread_id}}):
            await handle_event(event)
            live.update(await render())


async def main():
    console = Console()
    kb = KeyBindings()

    @kb.add("c-c")
    def _(event):
        event.app.exit()

    # Toggles for panels
    show_dag = True
    show_log = True

    # Status bar renderer (Unicode-only, retro style)
    def _toolbar():
        run_id = os.getenv("RUN_ID") or "-"
        redis = os.getenv("REDIS_URL") or ("mem" if os.getenv("REDIS_DISABLE", "false").lower() in {"1","true","yes"} else "?")
        return f"RUN:{run_id}  REDIS:{redis}  [F2]Toggle DAG  [F3]Toggle LOG  [Ctrl+C]Exit"

    session = PromptSession(bottom_toolbar=_toolbar)

    console.print(Panel("CompilerAgent CLI (retro) — 输入任务或命令: :toggle dag | :toggle log | :help", border_style="white", box=SQUARE))
    agent = Agent()
    while True:
        try:
            text = await session.prompt_async("〉 ", key_bindings=kb)
        except (EOFError, KeyboardInterrupt):
            console.print("\n再见.")
            break
        cmd = text.strip()
        if not cmd:
            continue
        if cmd.startswith(":"):
            c = cmd[1:].strip().lower()
            if c in {"toggle dag", "td"}:
                show_dag = not show_dag
                console.print(Panel(f"DAG 显示: {'开' if show_dag else '关'}", border_style="white", box=SQUARE))
                continue
            if c in {"toggle log", "tl"}:
                show_log = not show_log
                console.print(Panel(f"LOG 显示: {'开' if show_log else '关'}", border_style="white", box=SQUARE))
                continue
            if c in {"help", "h"}:
                console.print("命令: :toggle dag | :toggle log | :help")
                continue
            console.print("未知命令")
            continue

        # Run a task with current toggles
        sess = os.getenv("RUN_ID") or None
        await _stream_run(agent, cmd, sess, console, show_dag, show_log)


if __name__ == "__main__":
    asyncio.run(main())

