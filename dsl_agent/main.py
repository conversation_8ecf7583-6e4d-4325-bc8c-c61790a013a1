import asyncio
import os
from pprint import pprint

from dsl_agent.agent import Agent
from dsl_agent.compiler.base import BaseEnvironment
from dsl_agent.logging_config import configure_logging, get_logger, set_run_id, log_span

async def main():
    """
    The main entrypoint for the agent.
    """
    configure_logging()
    log = get_logger(__name__)
    set_run_id(os.getenv("RUN_ID"))

    log.info("Creating Agent")
    agent = Agent()

    task = os.getenv("AGENT_TASK") or "List the files in the /data directory, get running processes, and create a test file."
    sess = os.getenv("RUN_ID")

    try:
        with log_span(log, "agent.run", task=task[:200], session_id=sess):
            final_results = await agent.run(task, session_id=sess)
        # Filter out the environment object itself for cleaner printing
        printable_results = {k: v for k, v in final_results.items() if not isinstance(v, BaseEnvironment)}
        log.info("Final results", extra={"keys": list(printable_results.keys())})
        print("\n" + "---" + " FINAL RESULTS" + " ---")
        pprint(printable_results)
    except Exception as e:
        log.error("Agent failed", extra={"error": str(e)})
        print("\n" + "---" + " AGENT FAILED" + " ---")
        print(e)

if __name__ == "__main__":
    asyncio.run(main())
