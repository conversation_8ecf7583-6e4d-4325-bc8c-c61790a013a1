from typing import Dict, Any, cast
import os

from dotenv import load_dotenv

from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.graph import StateGraph, END

from dsl_agent.logging_config import configure_logging, get_logger
from dsl_agent.agent_state import AgentState
from dsl_agent.assembler.compiler import Compiler
from dsl_agent.assembler.graph_builder import LangGraphBuilder
from dsl_agent.llm.manager import create_model
from dsl_agent.llm.context_builder import build_system_yaml, build_observation_yaml

class Agent:
    """
    The main agent class that uses a self-correction loop to execute tasks.
    It compiles the DSL to a native LangGraph graph for execution.
    """
    def __init__(self, max_retries: int = 2, model_name: str = "gemini-2.5-flash"):
        self.max_retries = max_retries
        load_dotenv() # Load environment variables from .env file
        configure_logging()
        self.log = get_logger(__name__)

        system_prompt_content = build_system_yaml()
        self.llm = create_model(model_name)
        self.llm_enabled = True
        self.system_message = SystemMessage(content=system_prompt_content)

        # Initialize the outer LangGraph workflow
        workflow = StateGraph(AgentState)

        # Define nodes (avoid colliding with state keys)
        workflow.add_node("plan_step", self._plan_node)
        workflow.add_node("compile_step", self._compile_node)
        workflow.add_node("execute_step", self._execute_node)

        # Define edges
        workflow.set_entry_point("plan_step")
        workflow.add_edge("plan_step", "compile_step")

        # Conditional edges from compile
        workflow.add_conditional_edges(
            "compile_step",
            self._decide_to_execute_or_replan,
            {
                "execute": "execute_step",
                "replan": "plan_step",
            },
        )

        # Conditional edges from execute
        workflow.add_conditional_edges(
            "execute_step",
            self._decide_to_end_or_replan,
            {
                "end": END,
                "replan": "plan_step",
            },
        )

        # Configure Redis checkpointer (optional)
        from dsl_agent.persistence.redis_config import get_redis_saver
        try:
            self.checkpointer = get_redis_saver()
        except Exception as e:
            self.log.warning("Redis checkpointer not available; falling back to in-memory runtime only", extra={"error": str(e)})
            self.checkpointer = None

        if self.checkpointer is not None:
            self.app = workflow.compile(checkpointer=self.checkpointer)
        else:
            self.app = workflow.compile()

    async def _plan_node(self, state: AgentState) -> Dict[str, Any]:
        self.log.info("Planning", extra={"attempt": state['retry_count'] + 1})

        # Build YAML observation combining passive and active info
        observation_yaml = build_observation_yaml(state)
        messages = [self.system_message, HumanMessage(content=observation_yaml)]

        response = await self.llm.ainvoke(messages)
        content = response.content or ""
        # Expect YAML with keys: decision, plan
        decision = "execute"
        plan = content
        try:
            import yaml
            parsed = yaml.safe_load(content) if content else {}
            if isinstance(parsed, dict):
                decision = str(parsed.get("decision", decision))
                plan = str(parsed.get("plan", ""))
        except Exception:
            pass

        self.log.debug("LLM decision", extra={"decision": decision})
        self.log.debug("Generated plan", extra={"plan": (plan or "")[:5000]})

        # If plan is empty, force replan decision
        if not plan:
            decision = "replan"

        # Record messages for this invoke using Annotated[List[str], add]
        msg_texts = [
            f"SYSTEM: {self.system_message.content[:2000]}",
            f"USER: {observation_yaml[:2000]}",
            f"ASSISTANT: {content[:2000]}",
        ]
        return {
            "plan": plan,
            "compilation_error": None,
            "execution_error": None,
            "retry_count": state['retry_count'] + 1,
            "messages": msg_texts,
            "_llm_decision": decision,
        }

    async def _compile_node(self, state: AgentState) -> Dict[str, Any]:
        self.log.info("Compiling")
        try:
            compiler = Compiler()
            plan_text = state.get("plan") or ""
            graph, task_map = compiler.compile(plan_text)
            self.log.info("Compilation succeeded", extra={"tasks": len(task_map)})
            return {"inner_graph": graph, "inner_graph_task_map": task_map, "compilation_error": None}
        except (SyntaxError, NameError, AttributeError, Exception) as e:
            self.log.error("Compilation failed", extra={"error": str(e)})
            return {"compilation_error": str(e)}

    async def _execute_node(self, state: AgentState) -> Dict[str, Any]:
        self.log.info("Executing")
        try:
            task_map = state.get("inner_graph_task_map") or {}
            graph = state.get("inner_graph") or {}
            builder = LangGraphBuilder(task_map)
            app = builder.build(graph)

            initial_state = {"task_results": {}, "live_objects": {}, "exec_trace": []}
            final_state = await app.ainvoke(initial_state)

            self.log.info("Execution succeeded")
            return {"final_results": final_state.get("task_results", {}), "execution_error": None, "exec_trace": final_state.get("exec_trace", [])}
        except Exception as e:
            self.log.error("Execution failed", extra={"error": str(e)})
            return {"execution_error": str(e)}

    def _decide_to_execute_or_replan(self, state: AgentState) -> str:
        # Respect LLM's own decision when available
        llm_decision = state.get("_llm_decision")
        if llm_decision in {"execute", "replan"}:
            return cast(str, llm_decision)

        if state["compilation_error"] and state["retry_count"] < self.max_retries:
            return "replan"
        elif state["compilation_error"] and state["retry_count"] >= self.max_retries:
            raise RuntimeError(f"Failed to compile plan after {self.max_retries} attempts. Last error: {state['compilation_error']}")
        else:
            return "execute"

    def _decide_to_end_or_replan(self, state: AgentState) -> str:
        if state["execution_error"] and state["retry_count"] < self.max_retries:
            return "replan"
        elif state["execution_error"] and state["retry_count"] >= self.max_retries:
            raise RuntimeError(f"Failed to execute plan after {self.max_retries} attempts. Last error: {state['execution_error']}")
        else:
            return "end"

    async def run(self, task: str, session_id: str | None = None) -> Dict[str, Any]:
        initial_state: AgentState = {
            "task": task,
            "plan": None,
            "compilation_error": None,
            "execution_error": None,
            "final_results": None,
            "retry_count": 0,
            "messages": [],
            "session_id": None,
            "inner_graph": None,
            "inner_graph_task_map": None,
        }
        thread_id = session_id or os.getenv("RUN_ID") or "default"
        final_state = await self.app.ainvoke(initial_state, config={"configurable": {"thread_id": thread_id}})
        return final_state.get("final_results", {})