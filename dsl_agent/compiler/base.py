"""
This module defines the base classes and decorators for creating DSL environments and tools.
"""

import inspect
from typing import Dict, Type
from dsl_agent.environments.base_env import BaseEnvironment


def generate_dsl_schema(registry: Dict[str, Type[BaseEnvironment]]) -> str:
    """
    Generates a .pyi-style string of stubs for the registered environments.
    This uses introspection to build the schema dynamically.
    """
    schema_lines: list[str] = []

    for name, env_class in registry.items():
        schema_lines.append(f"class {name}:")
        if env_class.__doc__:
            docstring = inspect.cleandoc(env_class.__doc__)
            schema_lines.append(f'''    """
    {docstring}
    "''')

        # Add __init__ method
        try:
            init_sig = inspect.signature(env_class.__init__)
            # Format __init__ signature to include type hints
            params = []
            for param_name, param in init_sig.parameters.items():
                if param_name == 'self':
                    continue
                param_str = param_name
                if param.annotation is not inspect.Parameter.empty:
                    param_str += f": {param.annotation.__name__ if hasattr(param.annotation, '__name__') else str(param.annotation)}"
                if param.default is not inspect.Parameter.empty:
                    param_str += f" = {repr(param.default)}"
                params.append(param_str)
            init_params_str = ", ".join(params)
            schema_lines.append(f"    def __init__(self, {init_params_str}): ...\n")
        except ValueError:  # Handles cases like object.__init__ which has no signature
            pass

        # Add tool methods
        for method_name, method in inspect.getmembers(env_class, predicate=inspect.isfunction):
            if hasattr(method, '_is_env_tool'):
                method_sig = inspect.signature(method)
                
                # Format method signature to include type hints and default values
                params = []
                for param_name, param in method_sig.parameters.items():
                    if param_name == 'self':
                        continue
                    param_str = param_name
                    if param.annotation is not inspect.Parameter.empty:
                        param_str += f": {param.annotation.__name__ if hasattr(param.annotation, '__name__') else str(param.annotation)}"
                    if param.default is not inspect.Parameter.empty:
                        param_str += f" = {repr(param.default)}"
                    params.append(param_str)
                method_params_str = ", ".join(params)

                return_annotation = "" # Default to empty string
                if method_sig.return_annotation is not inspect.Parameter.empty:
                    return_annotation = f" -> {method_sig.return_annotation.__name__ if hasattr(method_sig.return_annotation, '__name__') else str(method_sig.return_annotation)}"

                schema_lines.append(f"    def {method_name}(self, {method_params_str}){return_annotation}: ...")
                if method.__doc__:
                    docstring = inspect.cleandoc(method.__doc__)
                    schema_lines.append(f'''        """
        {docstring}
        "''')
        schema_lines.append("\n")

    return "\n".join(schema_lines)
