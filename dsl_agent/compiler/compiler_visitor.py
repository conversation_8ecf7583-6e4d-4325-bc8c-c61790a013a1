import ast
from collections import defaultdict
from typing import Dict, Any, Set, Tuple, Optional, List
from enum import Enum
from dataclasses import dataclass

from dsl_agent.tool_manager import ENVIRONMENT_REGISTRY


class TaskType(Enum):
    """Enumeration of task types"""
    INSTANTIATION = "instantiation"
    METHOD_CALL = "method_call"
    CONDITIONAL_BRANCH = "conditional_branch"
    WHILE_LOOP = "while_loop"
    FOR_LOOP = "for_loop"
    SUBGRAPH = "subgraph"
    PLACEHOLDER = "placeholder"


class ComparisonOperator(Enum):
    """Enumeration of comparison operators"""
    EQ = "=="
    NE = "!="
    LT = "<"
    LE = "<="
    GT = ">"
    GE = ">="


class IteratorType(Enum):
    """Enumeration of iterator types"""
    VARIABLE = "variable"
    FUNCTION_CALL = "function_call"


@dataclass
class ConditionInfo:
    """Information about a condition (for if/while statements)"""
    variable: str
    operator: ComparisonOperator
    value: Any  # Can be str, int, float, bool, or variable name


@dataclass
class IteratorInfo:
    """Information about an iterator (for for loops)"""
    type: IteratorType
    variable: Optional[str] = None
    function: Optional[str] = None
    args: Optional[List[ast.AST]] = None
    keywords: Optional[List[ast.keyword]] = None


@dataclass
class SubgraphInfo:
    """Information about a subgraph"""
    name: str
    graph: Dict[str, Set[str]]
    task_map: Dict[str, Dict[str, Any]]
    all_tasks: List[str]

class DSLVisitor(ast.NodeVisitor):
    def __init__(self):
        # Track defined variables to catch undefined variable usage
        self.defined_vars: Set[str] = set()

        self.graph: Dict[str, Set[str]] = defaultdict(set)
        self.task_map: Dict[str, Dict[str, Any]] = {}
        self.all_tasks: List[str] = []  # Ordered list of tasks for sequential dependencies
        self.current_parent_task: Optional[str] = None  # For conditional branches

    def _get_operator_string(self, op: ast.cmpop) -> ComparisonOperator:
        """Convert AST comparison operator to ComparisonOperator enum"""
        operator_map = {
            ast.Eq: ComparisonOperator.EQ,
            ast.NotEq: ComparisonOperator.NE,
            ast.Lt: ComparisonOperator.LT,
            ast.LtE: ComparisonOperator.LE,
            ast.Gt: ComparisonOperator.GT,
            ast.GtE: ComparisonOperator.GE,
        }
        op_type = type(op)
        if op_type not in operator_map:
            raise SyntaxError(f"Unsupported comparison operator: {op_type.__name__}")
        return operator_map[op_type]

    def _extract_condition_info(self, condition_node: ast.Compare) -> ConditionInfo:
        """Extract condition information from a Compare node"""
        if (not isinstance(condition_node.left, ast.Name) or
            len(condition_node.ops) != 1 or
            not isinstance(condition_node.comparators[0], (ast.Constant, ast.Name))):
            raise SyntaxError("Unsupported condition format. Only 'var op value' is supported.")

        variable = condition_node.left.id
        operator = self._get_operator_string(condition_node.ops[0])

        comparator = condition_node.comparators[0]
        if isinstance(comparator, ast.Constant):
            value = comparator.value
        else:  # Must be ast.Name
            value = comparator.id

        return ConditionInfo(variable=variable, operator=operator, value=value)

    def _extract_dependencies_from_args(self, args: List[ast.expr], keywords: List[ast.keyword]) -> Set[str]:
        """Extract variable dependencies from function arguments"""
        # Import here to avoid circular imports
        from dsl_agent.compiler.ast_utils import ASTArgumentExtractor
        return ASTArgumentExtractor.extract_variable_dependencies(args, keywords)

    def _create_unique_task_name(self, prefix: str, node: ast.stmt) -> str:
        """Create a unique task name based on prefix and node location"""
        from dsl_agent.compiler.ast_utils import ASTNodeHelper
        return ASTNodeHelper.create_unique_name(prefix, node)

    def _add_sequential_dependency(self, task_name: str) -> None:
        """Add dependency to the previous task in sequence"""
        if len(self.all_tasks) > 1 and self.all_tasks[-2] != task_name:
            self.graph[task_name].add(self.all_tasks[-2])

    def _create_subgraph(self, statements: List[ast.stmt], parent_task: str, subgraph_prefix: str, node: ast.stmt) -> Optional[SubgraphInfo]:
        """Create a subgraph from a list of statements"""
        if not statements:
            return None

        subgraph_visitor = DSLVisitor()
        subgraph_visitor.current_parent_task = parent_task

        for stmt in statements:
            subgraph_visitor.visit(stmt)
        subgraph_visitor.finalize()

        if not subgraph_visitor.all_tasks:
            return None

        subgraph_name = self._create_unique_task_name(subgraph_prefix, node)
        return SubgraphInfo(
            name=subgraph_name,
            graph=subgraph_visitor.graph,
            task_map=subgraph_visitor.task_map,
            all_tasks=subgraph_visitor.all_tasks
        )

    def _register_subgraph(self, subgraph_info: SubgraphInfo, parent_task: str) -> None:
        """Register a subgraph in the task map and graph"""
        self.all_tasks.append(subgraph_info.name)
        self.task_map[subgraph_info.name] = {
            "type": TaskType.SUBGRAPH.value,
            "graph": subgraph_info.graph,
            "task_map": subgraph_info.task_map,
            "all_tasks": subgraph_info.all_tasks,
        }
        self.graph[subgraph_info.name].add(parent_task)

    def _extract_call_info(self, call_node: ast.Call, task_name: str) -> Tuple[TaskType, Set[str]]:
        """Extract call information and register task"""
        dependencies: Set[str] = set()

        # Extract dependencies from arguments
        dependencies.update(self._extract_dependencies_from_args(call_node.args, call_node.keywords))

        if isinstance(call_node.func, ast.Name):
            func_name = call_node.func.id
            if func_name in ENVIRONMENT_REGISTRY:
                task_type = TaskType.INSTANTIATION
                self.task_map[task_name] = {
                    "type": task_type.value,
                    "class_name": func_name,
                    "args": call_node.args,
                    "keywords": call_node.keywords,
                }
            else:
                raise NameError(f"Environment class '{func_name}' not found in registry.")

        elif isinstance(call_node.func, ast.Attribute):
            if not isinstance(call_node.func.value, ast.Name):
                raise TypeError("Only simple attribute access (obj.method) is supported")

            method_name = call_node.func.attr
            env_var = call_node.func.value.id
            # Static check: referenced environment must be defined earlier
            if env_var not in self.defined_vars:
                line = getattr(call_node, "lineno", "?")
                col = getattr(call_node, "col_offset", "?")
                raise NameError(f"Undefined environment variable '{env_var}' at {line}:{col}. Did you forget to instantiate it?")
            dependencies.add(env_var)
            task_type = TaskType.METHOD_CALL
            self.task_map[task_name] = {
                "type": task_type.value,
                "env_var": env_var,
                "method": method_name,
                "args": call_node.args,
                "keywords": call_node.keywords,
            }
        else:
            raise TypeError(f"Unsupported call function type: {type(call_node.func)}")

        return task_type, dependencies

    def _process_call_node(self, call_node: ast.Call, task_name: str) -> None:
        """Process a function call node and register it as a task"""
        self.all_tasks.append(task_name)
        _, dependencies = self._extract_call_info(call_node, task_name)
        self.graph[task_name].update(dependencies)
        # If in a conditional branch, add dependency to parent task
        if self.current_parent_task:
            self.graph[task_name].add(self.current_parent_task)

    def visit_Assign(self, node: ast.Assign):
        if isinstance(node.value, ast.Call) and isinstance(node.targets[0], ast.Name):
            task_name = node.targets[0].id
            self._process_call_node(node.value, task_name)
            # After successful processing, mark the assigned variable as defined
            self.defined_vars.add(task_name)

        self.generic_visit(node)

    def visit_Expr(self, node: ast.Expr):
        if isinstance(node.value, ast.Call):
            task_name = f"expr_{node.lineno}_{node.col_offset}"
            self._process_call_node(node.value, task_name)
        self.generic_visit(node)

    def visit_If(self, node: ast.If):
        # Create a unique task for the if condition itself
        conditional_node_name = self._create_unique_task_name("if_cond", node)
        self.all_tasks.append(conditional_node_name)

        # Extract condition details
        if not isinstance(node.test, ast.Compare):
            raise SyntaxError("Unsupported if condition format. Only comparison expressions are supported.")

        condition_info = self._extract_condition_info(node.test)

        self.task_map[conditional_node_name] = {
            "type": TaskType.CONDITIONAL_BRANCH.value,
            "condition_var": condition_info.variable,
            "operator": condition_info.operator.value,
            "value": condition_info.value,
        }
        self.graph[conditional_node_name].add(condition_info.variable)

        # Add sequential dependency to the conditional node
        self._add_sequential_dependency(conditional_node_name)

        # Store the current parent task to restore it later
        original_parent_task = self.current_parent_task
        self.current_parent_task = conditional_node_name

        # Process the if body as a subgraph
        if_subgraph_info = self._create_subgraph(node.body, conditional_node_name, "if_subgraph", node)
        if if_subgraph_info:
            self._register_subgraph(if_subgraph_info, conditional_node_name)

        # Process the else body as a subgraph
        else_subgraph_info = self._create_subgraph(node.orelse, conditional_node_name, "else_subgraph", node)
        if else_subgraph_info:
            self._register_subgraph(else_subgraph_info, conditional_node_name)

        # After visiting both branches, restore the original parent task
        self.current_parent_task = original_parent_task

        # Create continuation node after if/else block
        next_node_after_if = self._create_unique_task_name("after_if", node)
        self.all_tasks.append(next_node_after_if)
        self.task_map[next_node_after_if] = {"type": TaskType.PLACEHOLDER.value}
        self.graph[next_node_after_if] = set()

        # Add dependencies from the end of branches to the continuation node
        if if_subgraph_info:
            self.graph[next_node_after_if].add(if_subgraph_info.name)
        if else_subgraph_info:
            self.graph[next_node_after_if].add(else_subgraph_info.name)

        # Update task_map for conditional node with branch references
        self.task_map[conditional_node_name].update({
            "if_branch_start": if_subgraph_info.name if if_subgraph_info else None,
            "else_branch_start": else_subgraph_info.name if else_subgraph_info else None,
            "next_node_after_if": next_node_after_if,
        })

        # Do not call generic_visit here, as we manually visited children

    def visit_While(self, node: ast.While):
        # Create a unique task for the while loop
        while_node_name = self._create_unique_task_name("while_loop", node)
        self.all_tasks.append(while_node_name)
        self.graph[while_node_name] = set()

        # Extract condition details
        if not isinstance(node.test, ast.Compare):
            raise SyntaxError("Unsupported while condition format. Only comparison expressions are supported.")

        condition_info = self._extract_condition_info(node.test)

        # Add dependency on the condition variable
        self.graph[while_node_name].add(condition_info.variable)

        # Add sequential dependency if there are previous tasks
        self._add_sequential_dependency(while_node_name)

        # Process the while body as a subgraph
        while_body_subgraph_info = self._create_subgraph(node.body, while_node_name, "while_subgraph", node)
        if while_body_subgraph_info:
            self._register_subgraph(while_body_subgraph_info, while_node_name)

        # Create the while loop task
        self.task_map[while_node_name] = {
            "type": TaskType.WHILE_LOOP.value,
            "condition_var": condition_info.variable,
            "operator": condition_info.operator.value,
            "value": condition_info.value,
            "body_subgraph": while_body_subgraph_info.name if while_body_subgraph_info else None,
        }

        # Create continuation node after the while loop
        after_while_node_name = self._create_unique_task_name("after_while", node)
        self.all_tasks.append(after_while_node_name)
        self.task_map[after_while_node_name] = {"type": TaskType.PLACEHOLDER.value}
        self.graph[after_while_node_name] = set()

        # The continuation depends on the while loop completion
        if while_body_subgraph_info:
            self.graph[after_while_node_name].add(while_body_subgraph_info.name)
        else:
            self.graph[after_while_node_name].add(while_node_name)

        # Update the while loop task with continuation info
        self.task_map[while_node_name]["next_node_after_while"] = after_while_node_name

    def visit_For(self, node: ast.For):
        # Create a unique task for the for loop
        for_node_name = f"for_loop_{node.lineno}_{node.col_offset}"
        self.all_tasks.append(for_node_name)

        # Initialize the graph entry for this for node
        self.graph[for_node_name] = set()

        # Extract target variable(s) - the loop variable(s)
        target_vars = []
        if isinstance(node.target, ast.Name):
            # Simple case: for i in items
            target_vars.append(node.target.id)
        elif isinstance(node.target, ast.Tuple):
            # Tuple unpacking: for a, b in items
            for elt in node.target.elts:
                if isinstance(elt, ast.Name):
                    target_vars.append(elt.id)
                else:
                    raise SyntaxError(f"Unsupported for loop target element: {type(elt).__name__}")
        else:
            raise SyntaxError(f"Unsupported for loop target: {type(node.target).__name__}")

        # Extract iterator information
        iterator_info = {}
        iterator_dependencies = set()

        if isinstance(node.iter, ast.Name):
            # Simple case: for item in items
            iterator_info = {
                "type": "variable",
                "variable": node.iter.id
            }
            iterator_dependencies.add(node.iter.id)

        elif isinstance(node.iter, ast.Call):
            # Function call case: for i in range(5) or for a, b in zip(list1, list2)
            if isinstance(node.iter.func, ast.Name):
                func_name = node.iter.func.id
                iterator_info = {
                    "type": "function_call",
                    "function": func_name,
                    "args": node.iter.args,
                    "keywords": node.iter.keywords
                }

                # Add dependencies from function arguments
                for arg in node.iter.args:
                    if isinstance(arg, ast.Name):
                        iterator_dependencies.add(arg.id)
                for kw in node.iter.keywords:
                    if isinstance(kw.value, ast.Name):
                        iterator_dependencies.add(kw.value.id)
            else:
                raise SyntaxError(f"Unsupported iterator function type: {type(node.iter.func).__name__}")
        else:
            raise SyntaxError(f"Unsupported iterator type: {type(node.iter).__name__}")

        # Add dependencies on iterator
        self.graph[for_node_name].update(iterator_dependencies)

        # Add sequential dependency if there are previous tasks
        if len(self.all_tasks) > 1 and self.all_tasks[-2] != for_node_name:
            self.graph[for_node_name].add(self.all_tasks[-2])

        # --- Process the for body as a subgraph ---
        for_body_subgraph_name = None
        if node.body:
            for_subgraph_visitor = DSLVisitor()
            # Set the parent task for the subgraph's internal nodes
            for_subgraph_visitor.current_parent_task = for_node_name
            for stmt in node.body:
                for_subgraph_visitor.visit(stmt)
            for_subgraph_visitor.finalize()

            if for_subgraph_visitor.all_tasks:
                for_body_subgraph_name = f"for_subgraph_{node.lineno}_{node.col_offset}"
                self.all_tasks.append(for_body_subgraph_name)
                self.task_map[for_body_subgraph_name] = {
                    "type": "subgraph",
                    "graph": for_subgraph_visitor.graph,
                    "task_map": for_subgraph_visitor.task_map,
                    "all_tasks": for_subgraph_visitor.all_tasks,
                }
                # The subgraph depends on the for loop node
                self.graph[for_body_subgraph_name].add(for_node_name)

        # --- Process the for else clause (if any) ---
        for_else_subgraph_name = None
        if node.orelse:
            for_else_subgraph_visitor = DSLVisitor()
            # Set the parent task for the subgraph's internal nodes
            for_else_subgraph_visitor.current_parent_task = for_node_name
            for stmt in node.orelse:
                for_else_subgraph_visitor.visit(stmt)
            for_else_subgraph_visitor.finalize()

            if for_else_subgraph_visitor.all_tasks:
                for_else_subgraph_name = f"for_else_subgraph_{node.lineno}_{node.col_offset}"
                self.all_tasks.append(for_else_subgraph_name)
                self.task_map[for_else_subgraph_name] = {
                    "type": "subgraph",
                    "graph": for_else_subgraph_visitor.graph,
                    "task_map": for_else_subgraph_visitor.task_map,
                    "all_tasks": for_else_subgraph_visitor.all_tasks,
                }
                # The else subgraph depends on the for loop node
                self.graph[for_else_subgraph_name].add(for_node_name)

        # Create the for loop task
        self.task_map[for_node_name] = {
            "type": "for_loop",
            "target_vars": target_vars,
            "iterator_info": iterator_info,
            "body_subgraph": for_body_subgraph_name,
            "else_subgraph": for_else_subgraph_name,
        }

        # Create a node that represents the continuation after the for loop
        after_for_node_name = f"after_for_{node.lineno}_{node.col_offset}"
        self.all_tasks.append(after_for_node_name)
        self.task_map[after_for_node_name] = {"type": "placeholder"}
        self.graph[after_for_node_name] = set()

        # The continuation depends on the for loop completion
        # Both body and else (if present) should complete before continuation
        if for_body_subgraph_name:
            self.graph[after_for_node_name].add(for_body_subgraph_name)
        if for_else_subgraph_name:
            self.graph[after_for_node_name].add(for_else_subgraph_name)
        if not for_body_subgraph_name and not for_else_subgraph_name:
            self.graph[after_for_node_name].add(for_node_name)

        # Update the for loop task with continuation info
        self.task_map[for_node_name]["next_node_after_for"] = after_for_node_name

    def finalize(self):
        # Second pass: add special dependencies for `close` methods
        for task_name in list(self.all_tasks): # Iterate over a copy as all_tasks might change
            task_info = self.task_map.get(task_name, {})
            if task_info.get("type") == "method_call" and task_info.get("method") == "close":
                env_var_to_close = task_info["env_var"]
                # Find all tasks that use this environment variable
                for other_task_name, other_task_info in self.task_map.items():
                    if other_task_name == task_name: continue
                    if other_task_info.get("type") == "method_call" and other_task_info.get("env_var") == env_var_to_close:
                        self.graph[task_name].add(other_task_name)

                # Also add dependencies to any conditional nodes that might lead to tasks using this environment
                for cond_task_name, cond_task_info in self.task_map.items():
                    if cond_task_info.get("type") == "conditional_branch":
                        # Check if any branch tasks use this environment
                        if_branch_start = cond_task_info.get("if_branch_start")
                        else_branch_start = cond_task_info.get("else_branch_start")

                        branch_uses_env = False
                        for branch_task_name in [if_branch_start, else_branch_start]:
                            if branch_task_name and branch_task_name in self.task_map:
                                branch_info = self.task_map[branch_task_name]
                                if (
                                    branch_info.get("type") == "method_call" and
                                    branch_info.get("env_var") == env_var_to_close
                                ):
                                    branch_uses_env = True
                                    break

                        if branch_uses_env:
                            # Make close depend on the last tasks of the if/else branches
                            # These are already added as dependencies to next_node_after_if
                            # So, we make close depend on next_node_after_if
                            next_node_after_if = cond_task_info.get("next_node_after_if")
                            if next_node_after_if:
                                self.graph[task_name].add(next_node_after_if)

    def generic_visit(self, node: ast.AST):
        # Default visit method, ensures all nodes are visited
        super().generic_visit(node)