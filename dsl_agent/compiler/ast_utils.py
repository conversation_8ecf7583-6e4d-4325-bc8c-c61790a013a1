"""
Shared utilities for AST processing and argument handling.
This module eliminates code duplication between compiler_visitor.py and graph_builder.py.
"""

import ast
from typing import Dict, Any, List, Set, Tuple, Union

from dsl_agent.compiler.compiler_visitor import TaskType, ComparisonOperator


class ASTArgumentExtractor:
    """Utility class for extracting and processing AST arguments"""
    
    @staticmethod
    def extract_variable_dependencies(args: List[ast.expr], keywords: List[ast.keyword]) -> Set[str]:
        """Extract variable dependencies from function arguments (compile-time)"""
        dependencies = set()
        for arg in args:
            if isinstance(arg, ast.Name):
                dependencies.add(arg.id)
        for kw in keywords:
            if isinstance(kw.value, ast.Name):
                dependencies.add(kw.value.id)
        return dependencies
    
    @staticmethod
    def resolve_runtime_args(args: List[ast.expr], keywords: List[ast.keyword], 
                           task_results: Dict[str, Any]) -> <PERSON>ple[List[Any], Dict[str, Any]]:
        """Resolve AST arguments to actual values at runtime"""
        resolved_args = []
        resolved_kwargs = {}

        for arg in args:
            if isinstance(arg, ast.Name):
                resolved_args.append(task_results[arg.id])
            elif isinstance(arg, ast.Constant):
                resolved_args.append(arg.value)
            else:
                raise TypeError(f"Unsupported positional argument type: {type(arg)}")

        for kw in keywords:
            if isinstance(kw.value, ast.Name):
                resolved_kwargs[kw.arg] = task_results[kw.value.id]
            elif isinstance(kw.value, ast.Constant):
                resolved_kwargs[kw.arg] = kw.value.value
            else:
                raise TypeError(f"Unsupported keyword argument value type: {type(kw.value)}")
        
        return resolved_args, resolved_kwargs


class ConditionEvaluator:
    """Utility class for condition evaluation"""
    
    @staticmethod
    def evaluate_condition(current_value: Any, operator: Union[str, ComparisonOperator], 
                         expected_value: Any) -> bool:
        """Evaluate a condition at runtime"""
        # Convert string operator to enum if needed
        if isinstance(operator, str):
            operator_map = {
                "==": ComparisonOperator.EQ,
                "!=": ComparisonOperator.NE,
                "<": ComparisonOperator.LT,
                "<=": ComparisonOperator.LE,
                ">": ComparisonOperator.GT,
                ">=": ComparisonOperator.GE,
            }
            if operator not in operator_map:
                raise ValueError(f"Unknown operator: {operator}")
            operator = operator_map[operator]
        
        # Evaluate based on operator
        if operator == ComparisonOperator.EQ:
            return current_value == expected_value
        elif operator == ComparisonOperator.NE:
            return current_value != expected_value
        elif operator == ComparisonOperator.LT:
            return current_value < expected_value
        elif operator == ComparisonOperator.LE:
            return current_value <= expected_value
        elif operator == ComparisonOperator.GT:
            return current_value > expected_value
        elif operator == ComparisonOperator.GE:
            return current_value >= expected_value
        else:
            raise ValueError(f"Unsupported operator: {operator}")


class TaskInfoValidator:
    """Utility class for validating task information"""
    
    @staticmethod
    def validate_task_type(task_info: Dict[str, Any]) -> TaskType:
        """Validate and return the task type"""
        task_type_str = task_info.get("type")
        if not task_type_str:
            raise ValueError("Task info missing 'type' field")
        
        try:
            return TaskType(task_type_str)
        except ValueError:
            raise ValueError(f"Unknown task type: {task_type_str}")
    
    @staticmethod
    def validate_conditional_task(task_info: Dict[str, Any]) -> Tuple[str, str, Any]:
        """Validate conditional task and return condition components"""
        required_fields = ["condition_var", "operator", "value"]
        for field in required_fields:
            if field not in task_info:
                raise ValueError(f"Conditional task missing required field: {field}")
        
        return (
            task_info["condition_var"],
            task_info["operator"], 
            task_info["value"]
        )
    
    @staticmethod
    def validate_method_call_task(task_info: Dict[str, Any]) -> Tuple[str, str]:
        """Validate method call task and return env_var and method name"""
        required_fields = ["env_var", "method"]
        for field in required_fields:
            if field not in task_info:
                raise ValueError(f"Method call task missing required field: {field}")
        
        return task_info["env_var"], task_info["method"]
    

    @staticmethod
    def validate_instantiation_task(task_info: Dict[str, Any]) -> str:
        """Validate instantiation task and return class name"""
        if "class_name" not in task_info:
            raise ValueError("Instantiation task missing 'class_name' field")
        
        return task_info["class_name"]


class ASTNodeHelper:
    """Helper utilities for AST node processing"""
    
    @staticmethod
    def create_unique_name(prefix: str, node: ast.stmt) -> str:
        """Create a unique name based on prefix and node location"""
        return f"{prefix}_{node.lineno}_{node.col_offset}"
    
    @staticmethod
    def extract_name_from_target(target: ast.expr) -> str:
        """Extract variable name from assignment target"""
        if isinstance(target, ast.Name):
            return target.id
        else:
            raise TypeError(f"Unsupported assignment target type: {type(target)}")
    
    @staticmethod
    def is_simple_name_node(node: ast.expr) -> bool:
        """Check if node is a simple Name node"""
        return isinstance(node, ast.Name)
    
    @staticmethod
    def is_constant_node(node: ast.expr) -> bool:
        """Check if node is a Constant node"""
        return isinstance(node, ast.Constant)
    
    @staticmethod
    def get_node_value(node: ast.expr) -> Any:
        """Get the value from a node (Name.id or Constant.value)"""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Constant):
            return node.value
        else:
            raise TypeError(f"Cannot extract value from node type: {type(node)}")


# Convenience functions for backward compatibility
def extract_dependencies_from_args(args: List[ast.expr], keywords: List[ast.keyword]) -> Set[str]:
    """Backward compatibility function"""
    return ASTArgumentExtractor.extract_variable_dependencies(args, keywords)


def resolve_args(args: List[ast.expr], keywords: List[ast.keyword], 
                task_results: Dict[str, Any]) -> Tuple[List[Any], Dict[str, Any]]:
    """Backward compatibility function"""
    return ASTArgumentExtractor.resolve_runtime_args(args, keywords, task_results)


def evaluate_condition(current_value: Any, operator: Union[str, ComparisonOperator], 
                      expected_value: Any) -> bool:
    """Backward compatibility function"""
    return ConditionEvaluator.evaluate_condition(current_value, operator, expected_value)
