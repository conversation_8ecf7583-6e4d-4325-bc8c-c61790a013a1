"""
Builds a LangGraph graph from the output of our custom DSL compiler.
"""

import asyncio
import time
import os
from typing import Dict, Any, Set
from langgraph.graph import StateGraph, END
from dsl_agent.graph_state import GraphState
from dsl_agent.tool_manager import ENVIRONMENT_REGISTRY
from dsl_agent.compiler.ast_utils import ASTArgumentExtractor, ConditionEvaluator, TaskInfoValidator

class LangGraphBuilder:
    """
    Dynamically builds and compiles a LangGraph executable app
    from a compiled DSL plan.
    """
    def __init__(self, task_map: Dict[str, Any]):
        self.task_map = task_map
        self.workflow = StateGraph(GraphState)

    def _create_node_function(self, task_name: str):
        """
        Creates a function suitable for a LangGraph node that executes a single task.
        """
        task_info = self.task_map[task_name]

        async def node_func(state: GraphState) -> Dict[str, Any]:
            from dsl_agent.logging_config import get_logger
            log = get_logger(__name__)
            log.debug("Executing node", extra={"node": task_name})
            live_objects = state["live_objects"]

            # Prepare trace event
            start_ts = time.time()
            trace_event: Dict[str, Any] = {
                "node": task_name,
                "started_at": start_ts,
                "type": None,
                "status": "started",
            }

            # Validate and get task type
            task_type = TaskInfoValidator.validate_task_type(task_info)
            trace_event["type"] = task_type.value

            try:
                if task_type.value == "conditional_branch":
                    # Conditional branch nodes don't execute anything
                    trace_event["status"] = "skipped"
                    return {"task_results": {task_name: "conditional_evaluated"}, "exec_trace": [trace_event]}

                elif task_type.value == "placeholder":
                    trace_event["status"] = "skipped"
                    return {"task_results": {task_name: "placeholder_passed"}, "exec_trace": [trace_event]}

                elif task_type.value == "subgraph":
                    # Recursively build and invoke the subgraph
                    subgraph_builder = LangGraphBuilder(task_info["task_map"])
                    subgraph_app = subgraph_builder.build(task_info["graph"])

                    # Invoke the subgraph with the current state
                    subgraph_final_state = await subgraph_app.ainvoke(state)

                    # Merge results from subgraph back into main state
                    state["task_results"].update(subgraph_final_state["task_results"])
                    state["live_objects"].update(subgraph_final_state["live_objects"])

                    trace_event["status"] = "ok"
                    trace_event["duration_ms"] = int((time.time() - start_ts) * 1000)
                    return {"task_results": {task_name: "subgraph_executed"}, "live_objects": state["live_objects"], "exec_trace": [trace_event]}

                # For other node types, resolve args and kwargs
                args, kwargs = ASTArgumentExtractor.resolve_runtime_args(
                    task_info["args"], task_info["keywords"], state["task_results"]
                )

                if task_type.value == "instantiation":
                    class_name = TaskInfoValidator.validate_instantiation_task(task_info)
                    env_class = ENVIRONMENT_REGISTRY[class_name]
                    instance = env_class(*args, **kwargs)
                    live_objects[task_name] = instance
                    trace_event["status"] = "ok"
                    trace_event["duration_ms"] = int((time.time() - start_ts) * 1000)
                    return {"live_objects": live_objects, "task_results": {task_name: instance}, "exec_trace": [trace_event]}

                elif task_type.value == "method_call":
                    env_var, method_name = TaskInfoValidator.validate_method_call_task(task_info)
                    instance = live_objects[env_var]
                    method_to_call = getattr(instance, method_name)

                    # Configurable timeout/retry policy
                    timeout_s = float(os.getenv("NODE_TIMEOUT_SEC", "300"))
                    max_retries = int(os.getenv("NODE_RETRIES", "0"))
                    backoff_ms = int(os.getenv("NODE_RETRY_BACKOFF_MS", "500"))
                    attempt = 0

                    while True:
                        try:
                            # Await the async method call with timeout
                            result = await asyncio.wait_for(method_to_call(*args, **kwargs), timeout=timeout_s)
                            trace_event["status"] = "ok"
                            break
                        except asyncio.TimeoutError:
                            attempt += 1
                            if attempt > max_retries:
                                trace_event["status"] = "error"
                                trace_event["error_type"] = "TimeoutError"
                                trace_event["error"] = f"Node timed out after {timeout_s}s"
                                result = None
                                break
                            await asyncio.sleep(backoff_ms / 1000.0)
                        except Exception as e:
                            attempt += 1
                            if attempt > max_retries:
                                trace_event["status"] = "error"
                                trace_event["error_type"] = type(e).__name__
                                trace_event["error"] = str(e)
                                result = None
                                break
                            await asyncio.sleep(backoff_ms / 1000.0)

                    trace_event["attempt"] = attempt
                    trace_event["duration_ms"] = int((time.time() - start_ts) * 1000)

                    if trace_event["status"] == "error":
                        msg = f"[node_exec] {task_name} status={trace_event.get('status')}"
                        return {"exec_trace": [trace_event], "messages": [msg]}

                    # Sampling of non-error events
                    sample_rate_str = os.getenv("TRACE_SAMPLE_RATE", "1.0")
                    try:
                        sample_rate = float(sample_rate_str)
                    except ValueError:
                        sample_rate = 1.0
                    if sample_rate < 1.0:
                        import random
                        if random.random() > sample_rate:
                            # Drop this event from trace (but keep result)
                            return {"task_results": {task_name: result}}

                    # Optional: store lightweight I/O summary under trace_event when enabled

                        # Append a minimal message record for this node execution
                        msg = f"[node_exec] {task_name} status={trace_event.get('status')}"
                        return {"task_results": {task_name: result}, "exec_trace": [trace_event], "messages": [msg]}

                    if os.getenv("TRACE_INCLUDE_IO", "false").lower() in {"1", "true", "yes", "y"}:
                        def _summ(v):
                            if isinstance(v, str) and len(v) > 200:
                                return v[:200] + "...<truncated>"
                            if isinstance(v, (list, tuple)):
                                return f"{type(v).__name__}[{len(v)}]"
                            if isinstance(v, dict):
                                return f"dict[{len(v)}]"
                            return str(type(v).__name__)
                        trace_event["io_summary"] = {
                            "args": [_summ(a) for a in args],
                            "kwargs": {k: _summ(v) for k, v in kwargs.items()},
                            "result": _summ(result),
                        }

                    return {"task_results": {task_name: result}, "exec_trace": [trace_event]}


                trace_event["status"] = "noop"
                trace_event["duration_ms"] = int((time.time() - start_ts) * 1000)
                return {"exec_trace": [trace_event]}

            except Exception as e:
                trace_event["status"] = "error"
                trace_event["error"] = str(e)
                trace_event["duration_ms"] = int((time.time() - start_ts) * 1000)
                # Keep partial results untouched; surface exec_trace for diagnostics
                return {"exec_trace": [trace_event]}


        return node_func

    def build(self, graph: Dict[str, Set[str]]):
        """
        Builds the entire graph, adding all nodes and edges.
        """
        # Get all nodes from both the graph and task_map
        # This ensures we include conditional branch nodes that might not be in the main graph
        all_nodes = set(graph.keys())
        all_nodes.update(self.task_map.keys())

        # Add all nodes to the graph
        for task_name in all_nodes:
            node_function = self._create_node_function(task_name)
            self.workflow.add_node(task_name, node_function)

        # Add all edges based on dependencies and conditional branches
        for node, preds in graph.items():
            task_info = self.task_map.get(node)
            if task_info:
                task_type = TaskInfoValidator.validate_task_type(task_info)

                if task_type.value == "conditional_branch":
                    # This is a conditional node
                    self._add_conditional_edges(node, preds, task_info)
                else:
                    # Regular node, add direct edges
                    self._add_direct_edges(node, preds)
            else:
                # Node without task info, add direct edges
                self._add_direct_edges(node, preds)

        # Finalize the graph with entry and end points
        return self._finalize_graph(graph, all_nodes)

    def _add_conditional_edges(self, node: str, preds: Set[str], task_info: Dict[str, Any]) -> None:
        """Add conditional edges for a conditional branch node"""
        # First, add direct edges from predecessors to this conditional node
        for pred in preds:
            self.workflow.add_edge(pred, node)

        # Then, add conditional edges from this node to its branches
        def make_condition_func(node_name):
            def condition_func(state: GraphState) -> str:
                # Get the task info for this specific node
                node_task_info = self.task_map[node_name]
                condition_var, operator, value = TaskInfoValidator.validate_conditional_task(node_task_info)

                # Get the current value from state
                current_value = state["task_results"].get(condition_var)

                # Evaluate the condition using shared utility
                condition_result = ConditionEvaluator.evaluate_condition(current_value, operator, value)

                return "if_branch" if condition_result else "else_branch"
            return condition_func

        # Create the mapping for conditional edges
        edge_mapping = {}
        if task_info.get("if_branch_start"):
            edge_mapping["if_branch"] = task_info["if_branch_start"]
        if task_info.get("else_branch_start"):
            edge_mapping["else_branch"] = task_info["else_branch_start"]

        self.workflow.add_conditional_edges(node, make_condition_func(node), edge_mapping)

    def _add_direct_edges(self, node: str, preds: Set[str]) -> None:
        """Add direct edges for a regular node"""
        for pred in preds:
            self.workflow.add_edge(pred, node)

    def _finalize_graph(self, graph: Dict[str, Set[str]], all_nodes: Set[str]):
        """Set entry and end points for the graph"""
        # Set the entry and end points
        entry_points = [n for n, deps in graph.items() if not deps]
        if not entry_points:
            raise ValueError("Graph has no entry points or is cyclic.")

        self.workflow.set_entry_point(entry_points[0])
        # For simplicity, if there are multiple entry points, create edges from the first
        for i in range(1, len(entry_points)):
            self.workflow.add_edge(entry_points[0], entry_points[i])

        # All nodes that have no successors lead to the end
        all_successors = {pred for preds in graph.values() for pred in preds}
        end_points = [n for n in all_nodes if n not in all_successors]
        for node in end_points:
            self.workflow.add_edge(node, END)

        return self.workflow.compile()