import ast
from typing import Dict, <PERSON>, <PERSON>, <PERSON><PERSON>, Optional

from typing import Dict, Any, Set, Tuple, Optional

FORBIDDEN_AST_NODES = (
    ast.Import, ast.ImportFrom, ast.With, ast.Try, ast.FunctionDef, ast.AsyncFunctionDef,
    ast.ClassDef, ast.Lambda, ast.Delete, ast.Raise, ast.Global, ast.Nonlocal,
    ast.Yield, ast.YieldFrom, ast.Await, ast.ListComp, ast.DictComp, ast.SetComp, ast.GeneratorExp,
)


from graphlib import TopologicalSorter, CycleError

from dsl_agent.compiler.compiler_visitor import DSLVisitor

class Compiler:
    """
    A stateless compiler that parses object-oriented Python DSL and compiles it
    into a dependency graph (DAG).
    """
    def _validate_ast(self, tree: ast.AST) -> None:
        for node in ast.walk(tree):
            if isinstance(node, FORBIDDEN_AST_NODES):
                raise SyntaxError(f"Forbidden syntax: {type(node).__name__} at line {getattr(node, 'lineno', '?')}:{getattr(node, 'col_offset', '?')}")



    def compile(self, code_string: str) -> Tuple[Dict[str, Set[str]], Dict[str, Any]]:
        """
        Takes a string of Python DSL code and returns a dependency graph and a task map.
        """
        tree = ast.parse(code_string)
        self._validate_ast(tree)

        # NOTE: Graph is built during visit(); run validations after finalize

        visitor = DSLVisitor()
        # Validate no isolated nodes and no cycles
        # 1) Cycles
        try:
            TopologicalSorter(visitor.graph).static_order()
        except CycleError as ce:
            raise SyntaxError(f"Cycle detected in task graph: {ce}")
        # 2) Isolated nodes: appear in neither deps nor as dependees
        all_nodes = set(visitor.graph.keys())
        referenced = set()
        for n, deps in visitor.graph.items():
            referenced.update(deps)
        isolated = [n for n in all_nodes if not visitor.graph[n] and n not in referenced]
        if isolated:
            raise SyntaxError(f"Isolated tasks with no dependencies and not referenced: {', '.join(isolated)}")

        visitor.visit(tree)
        visitor.finalize()
        return visitor.graph, visitor.task_map