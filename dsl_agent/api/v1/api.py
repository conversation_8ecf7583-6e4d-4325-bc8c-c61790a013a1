import os
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
import uvicorn
from dsl_agent.agent import Agent

app = FastAPI(title="CompilerAgent API", version="0.1")

class RunRequest(BaseModel):
    task: str
    session_id: str | None = None

@app.post("/run")
async def run(req: RunRequest):
    try:
        agent = Agent()
        result = await agent.run(req.task, session_id=req.session_id)
        return {"ok": True, "final_results": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

class StateResponse(BaseModel):
    message: str = "State inspection requires checkpointer integration; TBD"

@app.get("/state/{session_id}")
async def state(session_id: str):
    # Placeholder: would require querying checkpointer for thread state
    return StateResponse().model_dump()

if __name__ == "__main__":
    port = int(os.getenv("API_PORT", "8000"))
    uvicorn.run("agent.api:app", host="0.0.0.0", port=port, reload=False)

