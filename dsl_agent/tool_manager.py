from dsl_agent.environments.docker_terminal import DockerTerminal
from dsl_agent.environments.linux_terminal import LinuxTerminal
from dsl_agent.environments.local_terminal import LocalTerminal
from dsl_agent.environments.gatk_env import GATKEnv
from dsl_agent.environments.bwa_env import BWAEnv
from dsl_agent.environments.samtools_env import SamtoolsEnv
from dsl_agent.environments.fastqc_env import FastQCEnv, MultiQCEnv
from dsl_agent.environments.picard_env import PicardEnv
from dsl_agent.environments.bedtools_env import BedtoolsEnv
from dsl_agent.environments.bcftools_env import BcftoolsEnv

ENVIRONMENT_REGISTRY = {
    "DockerTerminal": DockerTerminal,
    "LinuxTerminal": LinuxTerminal,
    "LocalTerminal": LocalTerminal,
    "GATKEnv": GATKEnv,
    "BWAEnv": BWAEnv,
    "SamtoolsEnv": SamtoolsEnv,
    "FastQCEnv": FastQCEnv,
    "MultiQCEnv": MultiQCEnv,
    "PicardEnv": PicardEnv,
    "BedtoolsEnv": BedtoolsEnv,
    "BcftoolsEnv": BcftoolsEnv,
}
