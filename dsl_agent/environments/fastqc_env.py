"""
FastQCEnv
=========
Docker-based environment exposing FastQC for quality control, and optional MultiQC aggregation.
- Default image (FastQC): biocontainers/fastqc:v0.11.9_cv8
- Default image (MultiQC via python): ewels/multiqc:latest (can be adjusted)
"""
from typing import Optional
from dsl_agent.compiler.base import env_tool
from dsl_agent.environments.docker_terminal import DockerTerminal

DEFAULT_FASTQC_IMAGE = "biocontainers/fastqc:v0.11.9_cv8"
DEFAULT_MULTIQC_IMAGE = "ewels/multiqc:latest"


class FastQCEnv(DockerTerminal):
    def __init__(self, image: Optional[str] = None):
        super().__init__(image=image or DEFAULT_FASTQC_IMAGE)

    @env_tool
    async def run_fastqc(self, input_fastq: str, outdir: str = "fastqc_out", extra_args: str = "") -> str:
        """
        Run FastQC on a FASTQ file.
        """
        cmd = f"fastqc {extra_args} -o {outdir} {input_fastq}".strip()
        return await self.run(cmd)


    @env_tool
    async def check_output(self, path: str) -> str:
        from .utils import file_exists_size
        sz = file_exists_size(path)
        return f"size={sz}" if sz is not None else "missing"

class MultiQCEnv(DockerTerminal):
    def __init__(self, image: Optional[str] = None):
        super().__init__(image=image or DEFAULT_MULTIQC_IMAGE)

    @env_tool
    async def run_multiqc(self, input_dir: str = ".", outdir: str = "multiqc_out", extra_args: str = "") -> str:
        """
        Run MultiQC to aggregate QC reports.
        """
        cmd = f"multiqc {extra_args} -o {outdir} {input_dir}".strip()
        return await self.run(cmd)

