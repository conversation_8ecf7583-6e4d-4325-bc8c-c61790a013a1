"""
LinuxTerminal base environment: a generic stateful Linux shell interface.
- Provides run() to execute shell commands
- Provides read() to read local files
- Can be subclassed by LocalTerminal (host shell) and Docker-based terminals
"""

import asyncio
from pathlib import Path
from typing import Optional

from dsl_agent.compiler.base import BaseEnvironment, env_tool


class LinuxTerminal(BaseEnvironment):
    """
    Generic Linux terminal environment.
    Subclasses can override behavior (e.g., Docker-backed vs local shell).
    """

    def __init__(self, workdir: Optional[str] = None):
        self.workdir = Path(workdir) if workdir else Path.cwd()

    @env_tool
    async def run(self, command: str) -> str:
        """
        运行命令 (在本地shell中执行)
        """
        proc = await asyncio.create_subprocess_shell(
            command,
            cwd=str(self.workdir),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.STDOUT,
        )
        stdout, _ = await proc.communicate()
        output = (stdout or b"").decode("utf-8", errors="ignore").strip()
        if proc.returncode != 0:
            output += f"\nERROR (Exit Code: {proc.returncode})"
        return output

    @env_tool
    async def read(self, path: str) -> str:
        """
        读取交互/输出内容 (本地文件读取版本)
        """
        p = self.workdir.joinpath(path)
        try:
            return await asyncio.to_thread(p.read_text, encoding="utf-8")
        except FileNotFoundError:
            return f"ERROR: file not found: {p}"

    @env_tool
    async def close(self) -> None:
        """
        关闭环境 (本地shell无需特别处理)
        """
        return None

    @env_tool
    async def run_stateless(self, command: str) -> str:
        """
        无状态执行命令 (对本地而言等同于 run)
        """
        return await self.run(command)

