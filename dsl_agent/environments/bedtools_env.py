"""
BedtoolsEnv
===========
Docker-based environment for bedtools operations.
- Default image: quay.io/biocontainers/bedtools:2.31.0--hf5e1c6e_3
"""
from typing import Optional
from dsl_agent.compiler.base import env_tool
from dsl_agent.environments.docker_terminal import DockerTerminal

DEFAULT_BEDTOOLS_IMAGE = "quay.io/biocontainers/bedtools:2.31.0--hf5e1c6e_3"


class BedtoolsEnv(DockerTerminal):
    def __init__(self, image: Optional[str] = None):
        super().__init__(image=image or DEFAULT_BEDTOOLS_IMAGE)

    @env_tool
    async def intersect(self, a: str, b: str, output_bed: str, extra_args: str = "") -> str:
        """
        bedtools intersect -a A -b B > out
        """
        cmd = f"bedtools intersect {extra_args} -a {a} -b {b} > {output_bed}".strip()
        return await self.run(cmd)

