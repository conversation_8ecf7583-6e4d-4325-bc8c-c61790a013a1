"""
LocalTerminal: a LinuxTerminal that runs on the host and provides some extra tools
like simple web search/reading and LLM calls (placeholders for now).
"""

from typing import Optional
from dsl_agent.compiler.base import env_tool
from dsl_agent.environments.linux_terminal import LinuxTerminal


class LocalTerminal(LinuxTerminal):
    """本地终端 (继承 LinuxTerminal)"""

    def __init__(self, workdir: Optional[str] = None):
        super().__init__(workdir)

    @env_tool
    async def search_web(self, query: str) -> str:
        """
        简单的占位实现: 仅回显 query
        """
        return f"[search_web placeholder] {query}"

    @env_tool
    async def read_web(self, url: str) -> str:
        """
        简单的占位实现: 仅回显 url
        """
        return f"[read_web placeholder] {url}"

    @env_tool
    async def call_llm(self, prompt: str) -> str:
        """
        简单的占位实现: 仅回显 prompt
        """
        return f"[call_llm placeholder] {prompt}"

