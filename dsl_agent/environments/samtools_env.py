"""
SamtoolsEnv
===========
Docker-based environment exposing common samtools operations.
- Default image: biocontainers/samtools:v1.17-1-deb_cv1
- Working dir inside container: /data (host ./data mounted)

Common operations:
- view: convert SAM→BAM or filter
- sort: sort BAM
- index: create .bai index
- flagstat: summary stats
"""
from typing import Optional
from dsl_agent.compiler.base import env_tool
from dsl_agent.environments.docker_terminal import DockerTerminal

DEFAULT_SAMTOOLS_IMAGE = "biocontainers/samtools:v1.17-1-deb_cv1"


class SamtoolsEnv(DockerTerminal):
    def __init__(self, image: Optional[str] = None):
        super().__init__(image=image or DEFAULT_SAMTOOLS_IMAGE)

    @env_tool
    async def view(self, input_path: str, output_bam: str, extra_args: str = "-bS") -> str:
        """
        Convert SAM to BAM (default: -bS).
        Examples: view("aln.sam", "aln.bam"), or add filters in extra_args.
        """
        cmd = f"samtools view {extra_args} {input_path} > {output_bam}".strip()
        return await self.run(cmd)

    @env_tool
    async def sort(self, input_bam: str, output_bam: str, extra_args: str = "") -> str:
        """
        Sort BAM and write to output.
        Uses temp files under /data if needed.
        """
        cmd = f"samtools sort {extra_args} -o {output_bam} {input_bam}".strip()
        return await self.run(cmd)

    @env_tool
    async def check_output(self, path: str) -> str:
        from .utils import file_exists_size
        sz = file_exists_size(path)
        return f"size={sz}" if sz is not None else "missing"

    @env_tool
    async def index(self, input_bam: str, extra_args: str = "") -> str:
        """
        Create BAM index (.bai).
        """
        cmd = f"samtools index {extra_args} {input_bam}".strip()
        return await self.run(cmd)

    @env_tool
    async def flagstat(self, input_bam: str) -> str:
        """
        Compute flagstat statistics.
        """
        cmd = f"samtools flagstat {input_bam}".strip()
        return await self.run(cmd)

