def env_tool(func):
    """
    Decorator to mark a method within a BaseEnvironment class as a tool
    callable from the DSL.
    """
    func._is_env_tool = True
    return func

class BaseEnvironment:
    """
    Base class for all DSL environments.
    Environments are stateful containers for a set of related tools.
    """
    def __repr__(self):
        return f"<{self.__class__.__name__} Environment>"
