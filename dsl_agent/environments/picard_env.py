"""
PicardEnv
=========
Docker-based environment exposing popular Picard tools.
- Default image: broadinstitute/picard:2.27.4 (can be adjusted)

Notes:
- GATK also includes Picard tools in some distributions, but keeping a separate environment
  makes dependencies explicit and images lightweight per responsibility.
"""
from typing import Optional
from dsl_agent.compiler.base import env_tool
from dsl_agent.environments.docker_terminal import DockerTerminal

DEFAULT_PICARD_IMAGE = "broadinstitute/picard:2.27.4"


class PicardEnv(DockerTerminal):
    def __init__(self, image: Optional[str] = None):
        super().__init__(image=image or DEFAULT_PICARD_IMAGE)

    @env_tool
    async def collect_insert_size_metrics(self, input_bam: str, output_metrics: str, histogram_pdf: str, extra_args: str = "") -> str:
        """
        Run CollectInsertSizeMetrics to compute insert size distribution and metrics.
        """
        cmd = (f"java -jar /usr/picard/picard.jar CollectInsertSizeMetrics "
               f"I={input_bam} O={output_metrics} H={histogram_pdf} {extra_args}").strip()
        return await self.run(cmd)

    @env_tool
    async def create_sequence_dictionary(self, reference_fasta: str, output_dict: Optional[str] = None, extra_args: str = "") -> str:
        """
        Create a sequence dictionary for a FASTA.
        """
        out = f"O={output_dict}" if output_dict else ""
        cmd = (f"java -jar /usr/picard/picard.jar CreateSequenceDictionary R={reference_fasta} {out} {extra_args}").strip()
        return await self.run(cmd)

