"""
GATKEnv
=======
- Docker-based environment for running GATK (Broad Institute) tools.
- Image: https://hub.docker.com/r/broadinstitute/gatk
- Digest (pinned): sha256:71b17ee42d149e8ec112603f5305c873ab60d93949ef8bb62a4fff85427f56fb
- Pull (informational): `docker pull broadinstitute/gatk`

Behavior
- Inherits DockerTerminal: a persistent container is launched with ./data mounted to /data.
- Working directory inside the container is /data.
- Methods decorated with @env_tool are exposed to the DSL and are awaited during execution.

Notes
- Docker SDK will pull the image automatically on first use if it's not present.
- We pin the image by digest to ensure reproducibility.
"""

from typing import Optional
from dsl_agent.compiler.base import env_tool
from dsl_agent.environments.docker_terminal import DockerTerminal

DEFAULT_GATK_IMAGE = (
    "broadinstitute/gatk@sha256:71b17ee42d149e8ec112603f5305c873ab60d93949ef8bb62a4fff85427f56fb"
)


class GATKEnv(DockerTerminal):
    """GATK environment backed by the official Broad Institute Docker image."""

    def __init__(self, image: Optional[str] = None):
        """
        Initialize a persistent GATK container.
        Args:
            image: Optional custom image reference. Defaults to the pinned digest.
        """
        super().__init__(image=image or DEFAULT_GATK_IMAGE)

    @env_tool
    async def run_gatk(self, args: str) -> str:
        """
        Run a raw GATK command-line by prefixing with `gatk`.
        Example: env.run_gatk("HaplotypeCaller -I in.bam -R ref.fa -O out.vcf.gz")
        """
        return await self.run(f"gatk {args}")

    @env_tool
    async def haplotype_caller(self, input_bam: str, reference_fasta: str, output_vcf_gz: str, extra_args: str = "") -> str:
        """
        Run GATK HaplotypeCaller.
        Args:
            input_bam: Input aligned BAM/CRAM file path (mounted under /data).
            reference_fasta: Reference FASTA path.
            output_vcf_gz: Output VCF(.gz) path.
            extra_args: Optional extra CLI args appended verbatim.
        Returns: Combined stdout (stderr merged); non-zero exit appends an ERROR line.
        """
        cmd = (
            f"gatk HaplotypeCaller -I {input_bam} -R {reference_fasta} -O {output_vcf_gz} "
            + (extra_args or "")
        ).strip()
        return await self.run(cmd)

    @env_tool
    async def base_recalibrator(self, input_bam: str, reference_fasta: str, known_sites_vcf: str, output_table: str, extra_args: str = "") -> str:
        """
        Run GATK BaseRecalibrator to generate a recalibration table.
        Args:
            input_bam: Input BAM/CRAM.
            reference_fasta: Reference FASTA path.
            known_sites_vcf: Known sites VCF (e.g., dbSNP).
            output_table: Output recalibration table path.
            extra_args: Optional extra CLI args.
        """
        cmd = (
            "gatk BaseRecalibrator "
            f"-I {input_bam} -R {reference_fasta} --known-sites {known_sites_vcf} -O {output_table} "
            + (extra_args or "")
        ).strip()
        return await self.run(cmd)

    @env_tool
    async def apply_bqsr(self, input_bam: str, reference_fasta: str, recal_table: str, output_bam: str, extra_args: str = "") -> str:
        """
        Apply BQSR to reads using a recalibration table.
        Args:
            input_bam: Input BAM/CRAM.
            reference_fasta: Reference FASTA.
            recal_table: Recalibration table produced by BaseRecalibrator.
            output_bam: Output BAM path.
            extra_args: Optional extra CLI args.
        """
        cmd = (
            "gatk ApplyBQSR "
            f"-I {input_bam} -R {reference_fasta} --bqsr-recal-file {recal_table} -O {output_bam} "
            + (extra_args or "")
        ).strip()
        return await self.run(cmd)

    @env_tool
    async def mark_duplicates(self, input_bam: str, output_bam: str, metrics_file: str, extra_args: str = "") -> str:
        """
        Mark duplicate reads using MarkDuplicatesSpark (distributed-capable) or MarkDuplicates.
        Defaults to Spark variant for performance; override via `extra_args` if needed.
        Args:
            input_bam: Input BAM/CRAM.
            output_bam: Output BAM path.
            metrics_file: Metrics output file path.
            extra_args: Optional extra CLI args (e.g., specify MarkDuplicates instead).
        """
        cmd = (
            "gatk MarkDuplicatesSpark "
            f"-I {input_bam} -O {output_bam} --metrics-file {metrics_file} "
            + (extra_args or "")
        ).strip()
        return await self.run(cmd)

    @env_tool
    async def genotype_gvcfs(self, reference_fasta: str, input_gvcf: str, output_vcf_gz: str, extra_args: str = "") -> str:
        """
        Jointly genotype GVCFs. `input_gvcf` can be a single GVCF.gz or a file-of-VCFs list prefixed with `--variant` via extra_args.
        Common patterns:
          - One file: -V sample.g.vcf.gz
          - Multiple: -V a.g.vcf.gz -V b.g.vcf.gz ... (supply via extra_args)
        """
        cmd = (f"gatk GenotypeGVCFs -R {reference_fasta} -V {input_gvcf} -O {output_vcf_gz} " + (extra_args or "")).strip()
        return await self.run(cmd)

    @env_tool
    async def gather_vcfs(self, inputs_list_file: str, output_vcf_gz: str, extra_args: str = "") -> str:
        """
        Gather (concatenate) multiple VCFs. Provide a file with one -I entry per line or pass -I via extra_args.
        Example list file content:
            -I part1.vcf.gz
            -I part2.vcf.gz
        """
        cmd = (f"gatk GatherVcfs -O {output_vcf_gz} -I {inputs_list_file} " + (extra_args or "")).strip()
        return await self.run(cmd)

    @env_tool
    async def variant_filtration(self, input_vcf_gz: str, output_vcf_gz: str, filter_expressions: str = "", extra_args: str = "") -> str:
        """
        Apply hard filters to a VCF. Provide expressions via `filter_expressions`, e.g., "--filter-expression 'QD < 2.0' --filter-name 'lowQD'".
        """
        cmd = (f"gatk VariantFiltration -V {input_vcf_gz} -O {output_vcf_gz} {filter_expressions} " + (extra_args or "")).strip()
        return await self.run(cmd)

    @env_tool
    async def split_intervals(self, reference_fasta: str, intervals: str, output_dir: str, extra_args: str = "") -> str:
        """
        Split intervals for scatter/gather. Example: -L exome_intervals.bed --scatter 10
        """
        cmd = (f"gatk SplitIntervals -R {reference_fasta} -L {intervals} -O {output_dir} " + (extra_args or "")).strip()
        return await self.run(cmd)

    @env_tool
    async def index_feature_file(self, input_vcf_gz: str) -> str:
        """Create index for a feature file (VCF.gz)."""
        cmd = f"gatk IndexFeatureFile -I {input_vcf_gz}"
        return await self.run(cmd)

    @env_tool
    async def validate_sam_file(self, input_bam: str, mode: str = "SUMMARY", extra_args: str = "") -> str:
        """
        Validate SAM/BAM file.
        Args:
            input_bam: Path to BAM/CRAM file
            mode: SUMMARY or VERBOSE
        """
        cmd = (f"gatk ValidateSamFile -I {input_bam} --MODE {mode} " + (extra_args or "")).strip()
        return await self.run(cmd)
    @env_tool
    async def check_output(self, path: str) -> str:
        """Return 'size=<bytes>' or 'missing' for quick post-check."""
        from .utils import file_exists_size
        sz = file_exists_size(path)
        return f"size={sz}" if sz is not None else "missing"

