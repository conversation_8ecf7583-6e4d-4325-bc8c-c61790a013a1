"""
BWAEnv
======
Docker-based environment exposing <PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>) commands to the DSL.
- Default image: biocontainers/bwa:v0.7.17_cv1 (override via constructor)
- Working dir inside container: /data (host ./data mounted)

Notes
- This environment focuses on producing SAM output from `bwa mem`; BAM conversion and sorting
  should be performed via SamtoolsEnv to keep environments single-responsibility.
- You can chain steps in the plan: generate SAM → samtools view/sort/index.
"""
from typing import Optional
from dsl_agent.compiler.base import env_tool
from dsl_agent.environments.docker_terminal import DockerTerminal

DEFAULT_BWA_IMAGE = "biocontainers/bwa:v0.7.17_cv1"


class BWAEnv(DockerTerminal):
    """BWA environment (paired- or single-end alignment)."""

    def __init__(self, image: Optional[str] = None):
        super().__init__(image=image or DEFAULT_BWA_IMAGE)

    @env_tool
    async def index(self, reference_fasta: str, extra_args: str = "") -> str:
        """
        Build BWA index for the given reference FASTA.
        Args:
            reference_fasta: Path under /data to reference FASTA (e.g., ref.fa)
            extra_args: Additional CLI args passed to `bwa index`
        Returns: stdout + error markers on failure
        """
        cmd = f"bwa index {extra_args} {reference_fasta}".strip()
        return await self.run(cmd)
    @env_tool
    async def check_output(self, path: str) -> str:
        from .utils import file_exists_size
        sz = file_exists_size(path)
        return f"size={sz}" if sz is not None else "missing"


    @env_tool
    async def mem_to_sam(self, reference_fasta: str, fastq1: str, fastq2: Optional[str], output_sam: str, extra_args: str = "") -> str:
        """
        Run `bwa mem` and write SAM to file. For single-end, set fastq2=None.
        Args:
            reference_fasta: Reference FASTA path
            fastq1: R1 FASTQ
            fastq2: R2 FASTQ or None for SE
            output_sam: Output SAM path
            extra_args: Additional args to `bwa mem` (e.g., -t 8)
        """
        if fastq2:
            cmd = f"bwa mem {extra_args} {reference_fasta} {fastq1} {fastq2} > {output_sam}"
        else:
            cmd = f"bwa mem {extra_args} {reference_fasta} {fastq1} > {output_sam}"
        return await self.run(cmd)

