"""
DockerTerminal: a LinuxTerminal that runs inside a dedicated Docker container.
- Aligns API with LinuxTerminal (run, read, close, run_stateless)
- Manages container lifecycle on instantiation (RAII)
"""

import asyncio
import docker
from docker.errors import DockerException, NotFound, ContainerError
import os
from pathlib import Path
from typing import Optional

from dsl_agent.compiler.base import env_tool
from dsl_agent.environments.linux_terminal import LinuxTerminal

class DockerTerminal(LinuxTerminal):
    """
    Docker-backed terminal environment. On creation, it launches a persistent container
    with the host ./data mounted to /data, and uses /data as working directory.
    """
    def __init__(self, image: str = "compiler-agent-env"):
        """
        Initializes the DockerEnvironment and starts a Docker container.

        Args:
            image: The Docker image to use for the container. Defaults to "compiler-agent-env".

        Raises:
            docker.errors.DockerException: If Docker is not running or not installed.
        """
        try:
            self.docker_client = docker.from_env()
        except DockerException:
            from dsl_agent.logging_config import get_logger
            get_logger(__name__).error("Docker is not running or not installed.")
            raise

        self.image = image
        self.container_id: Optional[str] = None
        self.host_data_dir = Path(os.getcwd()) / "data"
        self.host_data_dir.mkdir(exist_ok=True)

        # Start the container upon instantiation
        self._start_container()
        from dsl_agent.logging_config import get_logger
        get_logger(__name__).info("DockerTerminal initialized", extra={"container": self.container_id[:12]})

    def _start_container(self) -> None:
        """
        Starts a persistent Docker container.

        The container is configured to keep running indefinitely and mounts
        the host's 'data' directory to '/data' inside the container.
        """
        container = self.docker_client.containers.run(
            image=self.image,
            command="tail -f /dev/null",  # Keep container alive
            volumes={str(self.host_data_dir): {"bind": "/data", "mode": "rw"}},
            working_dir="/data",
            detach=True,
            remove=False,
        )
        self.container_id = container.id

    def _ensure_running(self) -> None:
        """Ensure the persistent container exists and is running; try to start if stopped."""
        if not self.container_id:
            raise RuntimeError("Container not running. The environment may have been closed.")
        try:
            container = self.docker_client.containers.get(self.container_id)
            container.reload()
            if container.status != "running":
                container.start()
        except NotFound:
            # Recreate if missing
            self._start_container()

    @env_tool
    async def run(self, command: str) -> str:
        """
        Runs a command in the persistent Docker container.

        - Wraps command with /bin/sh -lc for consistent shell semantics
        - Captures exit code and combined stdout/stderr; on non-zero, appends an error line
        - Optional behavior: DOCKER_RAISE_ON_NONZERO=true to raise on non-zero exit
        """
        self._ensure_running()
        raise_on_nonzero = os.getenv("DOCKER_RAISE_ON_NONZERO", "false").lower() in {"1", "true", "yes", "y"}

        def _exec_run() -> str:
            container = self.docker_client.containers.get(self.container_id or "")
            # Use demux to capture stdout/stderr separately if available
            try:
                exit_code, (stdout_b, stderr_b) = container.exec_run(["/bin/sh", "-lc", command], demux=True)
                stdout = (stdout_b or b"").decode("utf-8", errors="ignore").strip()
                stderr = (stderr_b or b"").decode("utf-8", errors="ignore").strip()
                output = stdout
            except Exception:
                # Fallback to combined stream
                exit_code, output_bytes = container.exec_run(["/bin/sh", "-lc", command])
                output = (output_bytes or b"").decode("utf-8", errors="ignore").strip()
                stderr = ""

            if exit_code != 0:
                from dsl_agent.logging_config import get_logger
                get_logger(__name__).warning("Docker command failed", extra={"exit_code": exit_code, "command": command})
                if raise_on_nonzero:
                    raise RuntimeError(f"Docker command failed with exit code {exit_code}: {stderr or output}")
                if stderr:
                    output += f"\nSTDERR:\n{stderr}"
                output += f"\nERROR (Exit Code: {exit_code})"
            return output

        from dsl_agent.logging_config import get_logger
        get_logger(__name__).debug("Docker run", extra={"container": self.container_id[:12], "command": command})
        return await asyncio.to_thread(_exec_run)

    @env_tool
    async def close(self) -> None:
        """
        Stops and removes the Docker container session.

        If no active container is found, a warning is printed.
        """
        if not self.container_id:
            from dsl_agent.logging_config import get_logger
            get_logger(__name__).warning("No active container to close")
            return

        def _stop_container() -> None:
            try:
                container = self.docker_client.containers.get(self.container_id or "")
                from dsl_agent.logging_config import get_logger
                get_logger(__name__).info("Stopping container", extra={"container": container.id[:12]})
                container.stop()
            except NotFound:
                from dsl_agent.logging_config import get_logger
                get_logger(__name__).warning("Container not found on close", extra={"container": str(self.container_id)[:12]})

        await asyncio.to_thread(_stop_container)
        from dsl_agent.logging_config import get_logger
        get_logger(__name__).info("Container stopped", extra={"container": self.container_id[:12]})
        self.container_id = None

    @env_tool
    async def run_stateless(self, command: str) -> str:
        """
        Runs a command in a new, ephemeral Docker container that is removed after execution.

        - Wraps command with /bin/sh -lc
        - On non-zero exit, returns stderr and marks error in the output string
        """
        def _run_ephemeral() -> str:
            try:
                container_output = self.docker_client.containers.run(
                    image=self.image,
                    command=f'/bin/sh -lc "{command}"',
                    volumes={str(self.host_data_dir): {"bind": "/data", "mode": "rw"}},
                    working_dir="/data",
                    remove=True,
                    stdout=True,
                    stderr=True,
                )
                return (container_output or b"").decode('utf-8', errors='ignore').strip()
            except ContainerError as e:
                # Standardized error message with exit status
                exit_status = getattr(e, "exit_status", None)
                stderr = getattr(e, "stderr", b"")
                err_txt = (stderr or b"").decode('utf-8', errors='ignore').strip()
                from dsl_agent.logging_config import get_logger
                get_logger(__name__).warning("Stateless command failed", extra={"command": command, "exit_code": exit_status})
                out = err_txt
                if exit_status is not None:
                    out += f"\nERROR (Exit Code: {exit_status})"
                return out

        from dsl_agent.logging_config import get_logger
        get_logger(__name__).debug("Docker stateless run", extra={"command": command})
        return await asyncio.to_thread(_run_ephemeral)

