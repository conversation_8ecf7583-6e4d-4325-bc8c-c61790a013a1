"""
BcftoolsEnv
===========
Docker-based environment for bcftools operations.
- Default image: quay.io/biocontainers/bcftools:1.18--h8b25389_0
"""
from typing import Optional
from dsl_agent.compiler.base import env_tool
from dsl_agent.environments.docker_terminal import DockerTerminal

DEFAULT_BCFTOOLS_IMAGE = "quay.io/biocontainers/bcftools:1.18--h8b25389_0"


class BcftoolsEnv(DockerTerminal):
    def __init__(self, image: Optional[str] = None):
        super().__init__(image=image or DEFAULT_BCFTOOLS_IMAGE)

    @env_tool
    async def index(self, input_vcf_gz: str) -> str:
        """Create CSI/Tabix index for VCF.gz."""
        cmd = f"bcftools index {input_vcf_gz}"
        return await self.run(cmd)

    @env_tool
    async def view(self, input_vcf_gz: str, region: Optional[str] = None, output_vcf_gz: Optional[str] = None, extra_args: str = "") -> str:
        """
        Extract regions/records and optional write to output.
        """
        region_part = f"-r {region} " if region else ""
        out_part = f"> {output_vcf_gz}" if output_vcf_gz else ""
        cmd = (f"bcftools view {extra_args} {region_part}{input_vcf_gz} {out_part}").strip()
        return await self.run(cmd)

