from typing import TypedDict, Dict, Any, Optional
from typing import List, Annotated
from dsl_agent.graph_state import add_messages
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.checkpoint.memory import MemorySaver


class AgentState(TypedDict):
    """
    Represents the state of the overall agent workflow.
    """
    task: str
    plan: Optional[str]
    compilation_error: Optional[str]
    execution_error: Optional[str]
    final_results: Optional[Dict[str, Any]]
    retry_count: int
    messages: Annotated[List[str], add_messages]
    session_id: Optional[str]

    inner_graph: Optional[Dict[str, Any]] # Store the graph structure
    inner_graph_task_map: Optional[Dict[str, Any]]