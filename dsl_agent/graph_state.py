"""
Defines the state for the LangGraph graph.
"""

from typing import TypedDict, Dict, Any, Annotated, List

def merge_dicts(left: Dict[str, Any], right: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merges two dictionaries, with values from the right dictionary overwriting
    values from the left dictionary in case of duplicate keys.
    """
    return {**left, **right}

def append_trace(left: List[Dict[str, Any]], right: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Concatenate two lists for exec_trace."""
    return (left or []) + (right or [])

def add_messages(left: List[str], right: List[str]) -> List[str]:
    """LangGraph-style add for messages: concatenate string lists."""
    return (left or []) + (right or [])

class GraphState(TypedDict):
    """
    Represents the state of our graph.

    Attributes:
        task_results: A dictionary to store the output of each task.
        live_objects: A dictionary to store instantiated environment objects.
        exec_trace: A list of execution events (per node), appended in order.
        messages: A list of chat-like messages exchanged during planning/execution.

    """
    task_results: Annotated[Dict[str, Any], merge_dicts]
    live_objects: Dict[str, Any]
    exec_trace: Annotated[List[Dict[str, Any]], append_trace]
    messages: Annotated[List[str], add_messages]

