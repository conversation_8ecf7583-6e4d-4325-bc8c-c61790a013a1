"""
Centralized logging configuration and helpers.

Usage:
- Call configure_logging() once early (e.g., in Agent __init__ or app entrypoint)
- Get a module logger via get_logger(__name__) and use .debug/.info/.warning/.error

Env vars:
- LOG_LEVEL: DEBUG|INFO|WARNING|ERROR (default INFO)
- LOG_FORMAT: plain|json (default plain)
- LOG_FILE: path to write logs (default stderr)
"""
from __future__ import annotations
import logging
import os
import sys
import json
import time
from contextvars import ContextVar
from contextlib import contextmanager
from typing import Optional, Iterator

_JSON_FIELDS = [
    "asctime", "levelname", "name", "message"
]

# Correlation ID context (per-run/session)
_RUN_ID: ContextVar[Optional[str]] = ContextVar("run_id", default=None)

class JsonFormatter(logging.Formatter):
    def format(self, record: logging.LogRecord) -> str:
        base = {
            "asctime": self.formatTime(record, self.datefmt),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
        }
        # Attach run_id if present
        run_id = getattr(record, "run_id", None)
        if run_id is not None:
            base["run_id"] = run_id
        # include extras if present
        for k, v in getattr(record, "__dict__", {}).items():
            if k in ("args", "msg", "exc_info", "exc_text", "stack_info", "stacklevel", "msecs", "relativeCreated", "levelno", "created", "thread", "threadName", "processName", "process"):
                continue
            if k not in base and not k.startswith("_"):
                try:
                    json.dumps({k: v})
                    base[k] = v
                except Exception:
                    base[k] = str(v)
        return json.dumps(base, ensure_ascii=False)

class ContextFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        run_id = _RUN_ID.get()
        if run_id is not None and not hasattr(record, "run_id"):
            setattr(record, "run_id", run_id)
        return True

_configured = False

def configure_logging(level: Optional[str] = None, fmt: Optional[str] = None, logfile: Optional[str] = None) -> None:
    """Configure root logger once. Idempotent."""
    global _configured
    if _configured:
        return

    level_str = (level or os.getenv("LOG_LEVEL", "INFO")).upper()
    fmt_str = (fmt or os.getenv("LOG_FORMAT", "plain")).lower()
    file_path = logfile or os.getenv("LOG_FILE")

    lvl = getattr(logging, level_str, logging.INFO)

    handlers = []
    # Optional rotation
    rotate = os.getenv("LOG_ROTATE", "false").lower() in {"1", "true", "yes"}
    if file_path:
        if rotate:
            try:
                from logging.handlers import RotatingFileHandler
                max_bytes = int(os.getenv("LOG_MAX_BYTES", str(10 * 1024 * 1024)))
                backup_count = int(os.getenv("LOG_BACKUP_COUNT", "5"))
                handlers.append(RotatingFileHandler(file_path, maxBytes=max_bytes, backupCount=backup_count))
            except Exception:
                handlers.append(logging.FileHandler(file_path))
        else:
            handlers.append(logging.FileHandler(file_path))
    else:
        handlers.append(logging.StreamHandler(sys.stderr))

    if fmt_str == "json":
        formatter = JsonFormatter()
    else:
        formatter = logging.Formatter(fmt="%(asctime)s | %(levelname)s | %(name)s | %(message)s", datefmt="%Y-%m-%d %H:%M:%S")

    ctx_filter = ContextFilter()
    for h in handlers:
        h.setFormatter(formatter)
        h.addFilter(ctx_filter)

    root = logging.getLogger()
    root.setLevel(lvl)
    root.addFilter(ctx_filter)  # ensure run_id attaches for any existing handlers
    # Avoid duplicate handlers if someone else configured
    if not root.handlers:
        for h in handlers:
            root.addHandler(h)
    _configured = True


def get_logger(name: str) -> logging.Logger:
    return logging.getLogger(name)


def set_run_id(run_id: Optional[str]) -> None:
    _RUN_ID.set(run_id)


@contextmanager
def log_span(logger: logging.Logger, name: str, level: int = logging.INFO, **extras) -> Iterator[None]:
    start = time.time()
    logger.log(level, f"Start {name}", extra=extras if extras else None)
    try:
        yield
        dur = int((time.time() - start) * 1000)
        ex = dict(extras) if extras else {}
        ex["duration_ms"] = dur
        logger.log(level, f"End {name}", extra=ex)
    except Exception as e:
        dur = int((time.time() - start) * 1000)
        ex = dict(extras) if extras else {}
        ex["duration_ms"] = dur
        ex["error"] = str(e)
        logger.error(f"Error {name}", extra=ex)
        raise

