"""
LLM context builders (unified): system YAML and observation YAML.
- System YAML: compile a structured YAML prompt by merging the MD template + DSL schema
- Observation YAML: snapshot of current context for the LLM
"""
import os
from typing import Any, Dict, List
import yaml

from dsl_agent.tool_manager import ENVIRONMENT_REGISTRY
from dsl_agent.compiler.base import generate_dsl_schema
from dsl_agent.agent_state import AgentState
from .observation import (
    assemble_observation_dict,
    sanitize_observation,
    to_yaml as obs_to_yaml,
)

TEMPLATE_PATH = os.path.join(os.getcwd(), "prompts", "system_prompt_template.md")


def _read_template_lines() -> List[str]:
    try:
        with open(TEMPLATE_PATH, "r", encoding="utf-8") as f:
            return f.read().splitlines()
    except Exception:
        return []


def _parse_system_template_md(lines: List[str]) -> Dict[str, Any]:
    """Very small parser to extract sections from the MD template into structured data."""
    sections: Dict[str, List[str]] = {}
    current: str | None = None
    in_code = False
    code_buf: List[str] = []

    for line in lines:
        if line.startswith("```"):
            in_code = not in_code
            if not in_code:
                # code block ended; attach to current section with key 'code'
                key = f"{current or 'code'}:code"
                sections.setdefault(key, [])
                sections[key].extend(code_buf)
                code_buf = []
            continue
        if in_code:
            code_buf.append(line)
            continue
        if line.startswith("# "):
            current = line[2:].strip().lower().replace(" ", "_")
            sections.setdefault(current, [])
        else:
            if current is None:
                continue
            sections[current].append(line.rstrip())

    # Build structured fields
    def _join(name: str) -> str:
        return "\n".join([l for l in sections.get(name, []) if l.strip()])

    def _list(name: str) -> List[str]:
        return [l.strip("- ") for l in sections.get(name, []) if l.strip()]

    def _code(name: str) -> str:
        return "\n".join(sections.get(f"{name}:code", []))

    identity_purpose = _join("identity_and_purpose")
    operational_context = _list("operational_context")
    interaction_protocol = {
        "observation_log_format": _code("interaction_protocol")
    }
    action_space_intro = _join("action_space")
    directives = _list("core_directives")

    return {
        "identity_and_purpose": identity_purpose,
        "operational_context": {
            "items": operational_context,
        },
        "interaction_protocol": interaction_protocol,
        "action_space_intro": action_space_intro,
        "directives": directives,
    }


def build_system_yaml() -> str:
    """Build structured system YAML by merging template-derived fields and DSL schema.

    The LLM is asked to reply with YAML of the form:
      decision: execute|replan
      plan: |  # Python DSL only
        <single DSL expression>
    """
    schema_pyi = generate_dsl_schema(ENVIRONMENT_REGISTRY)
    parsed = _parse_system_template_md(_read_template_lines())

    system = {
        "schema_version": 1,
        "meta": {
            "identity_and_purpose": parsed.get("identity_and_purpose", ""),
        },
        "operational_context": parsed.get("operational_context", {}),
        "interaction_protocol": parsed.get("interaction_protocol", {}),
        "action_space": {
            "intro": parsed.get("action_space_intro", ""),
            "dsl_schema_pyi": schema_pyi,
        },
        "directives": parsed.get("directives", []),
        "response_contract": {
            "format": "yaml",
            "schema": {
                "decision": {"type": "string", "enum": ["execute", "replan"]},
                "plan": {"type": "string", "description": "Python DSL code only (single expression)"},
            },
            "notes": [
                "Return decision and plan only. No extra commentary.",
                "If prior step failed, you may set decision to replan and provide a corrected plan.",
            ],
        },
    }
    return yaml.safe_dump(system, sort_keys=False, allow_unicode=True)


def build_observation_yaml(state: AgentState) -> str:
    obs_dict = assemble_observation_dict(
        task=state.get("task", ""),
        retry_count=state.get("retry_count", 0),
        compilation_error=state.get("compilation_error"),
        execution_error=state.get("execution_error"),
        previous_plan=state.get("plan"),
        final_results=state.get("final_results"),
        exec_trace=state.get("exec_trace"),
    )
    # add schema_version at top-level
    wrapped: Dict[str, Any] = {"schema_version": 1}
    wrapped.update(obs_dict)  # type: ignore[arg-type]

    sanitized = sanitize_observation(wrapped)  # type: ignore[arg-type]

    # Optional: apply overall budget (soft limit) on YAML length
    soft_limit = int(os.getenv("OBS_YAML_SOFT_LIMIT_BYTES", "131072"))  # 128KB default
    yaml_txt = obs_to_yaml(sanitized)
    if len(yaml_txt.encode("utf-8")) > soft_limit:
        # Append a truncation notice
        yaml_txt = yaml_txt[:soft_limit] + "\n# <truncated: soft limit reached>\n"

    header = "# Observation (YAML)\n"
    return header + yaml_txt


def sanitize_for_yaml(data: Any) -> Any:
    """Sanitize data so it can be safely serialized to YAML (truncate/convert)."""
    # Truncate large strings
    if isinstance(data, str):
        if len(data) > 4000:
            return data[:4000] + "...<truncated>"
        return data
    # Primitives
    if data is None or isinstance(data, (int, float, bool)):
        return data
    # Mappings
    if isinstance(data, dict):
        return {str(k): sanitize_for_yaml(v) for k, v in data.items()}
    # Sequences
    if isinstance(data, (list, tuple)):
        max_len = 50
        items = list(data)
        if len(items) > max_len:
            items = items[:max_len] + ["<truncated>"]
        return [sanitize_for_yaml(v) for v in items]
    # Fallback to string with type name
    return f"<{type(data).__name__}>"

