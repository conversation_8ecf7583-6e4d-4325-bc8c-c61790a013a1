"""
Observation schema, sanitizer and YAML serializer utilities (LLM layer).
"""
from typing import TypedDict, Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime
import os
import yaml


class PassiveObs(TypedDict, total=False):
    timestamp: str
    user_instructions: str
    async_returns: List[Dict[str, Any]]


class DSLOutputs(TypedDict, total=False):
    previous_plan: Optional[str]
    final_results: Optional[Dict[str, Any]]
    exec_trace: Optional[List[Dict[str, Any]]]


class ActiveObs(TypedDict, total=False):
    phase: str
    retry_count: int
    errors: Dict[str, Any]
    dsl_outputs: DSLOutputs


class ObservationBody(TypedDict, total=False):
    passive: PassiveObs
    active: ActiveObs


class Observation(TypedDict, total=False):
    observation: ObservationBody


@dataclass
class ObservationConfig:
    # Truncation limits (tunable). None means: read from env or use defaults.
    max_string: Optional[int] = None
    max_list: Optional[int] = None
    max_dict_items: Optional[int] = None
    include_exec_trace: Optional[bool] = None
    max_trace_events: Optional[int] = None  # limit number of trace events kept

    def __post_init__(self):
        def env_int(name: str, default: int) -> int:
            try:
                return int(os.getenv(name, str(default)))
            except Exception:
                return default
        def env_bool(name: str, default: bool) -> bool:
            val = os.getenv(name, None)
            if val is None:
                return default
            return val.lower() in {"1", "true", "yes", "y"}

        if self.max_string is None:
            self.max_string = env_int("OBS_MAX_STRING", 4000)
        if self.max_list is None:
            self.max_list = env_int("OBS_MAX_LIST", 200)
        if self.max_dict_items is None:
            self.max_dict_items = env_int("OBS_MAX_DICT_ITEMS", 200)
        if self.include_exec_trace is None:
            self.include_exec_trace = env_bool("OBS_INCLUDE_TRACE", True)
        if self.max_trace_events is None:
            self.max_trace_events = env_int("OBS_MAX_TRACE_EVENTS", 500)


def assemble_observation_dict(
    task: str,
    retry_count: int,
    compilation_error: Optional[str],
    execution_error: Optional[str],
    previous_plan: Optional[str],
    final_results: Optional[Dict[str, Any]],
    exec_trace: Optional[List[Dict[str, Any]]],
) -> Observation:
    """Build the raw observation dictionary before sanitization/serialization."""
    timestamp = datetime.now().isoformat(timespec="seconds")
    passive: PassiveObs = {
        "timestamp": timestamp,
        "user_instructions": task or "",
        "async_returns": [],  # reserved for async mailbox
    }
    phase = "replanning" if (compilation_error or execution_error) else "planning"
    dsl_outputs: DSLOutputs = {
        "previous_plan": previous_plan,
        "final_results": final_results,
        "exec_trace": exec_trace,
    }
    active: ActiveObs = {
        "phase": phase,
        "retry_count": retry_count,
        "errors": {
            "compilation": compilation_error,
            "execution": execution_error,
        },
        "dsl_outputs": dsl_outputs,
    }
    body: ObservationBody = {"passive": passive, "active": active}
    return {"observation": body}


def _sanitize(data: Any, cfg: ObservationConfig, depth: int = 0) -> Any:
    # Primitives
    if data is None or isinstance(data, (int, float, bool)):
        return data
    if isinstance(data, str):
        max_str = cfg.max_string or 4000
        if len(data) > max_str:
            return data[: max_str] + "...<truncated>"
        return data

    # Dicts
    if isinstance(data, dict):
        out: Dict[str, Any] = {}
        count = 0
        max_items = cfg.max_dict_items or 200
        for k, v in data.items():
            if count >= max_items:
                out["<truncated>"] = "..."
                break
            out[str(k)] = _sanitize(v, cfg, depth + 1)
            count += 1
        return out

    # Lists/Tuples
    if isinstance(data, (list, tuple)):
        items = list(data)
        max_list = cfg.max_list or 200
        if len(items) > max_list:
            items = items[: max_list] + ["<truncated>"]
        return [_sanitize(v, cfg, depth + 1) for v in items]

    # Fallback: type name
    return f"<{type(data).__name__}>"


def sanitize_observation(obs: Observation, cfg: Optional[ObservationConfig] = None) -> Observation:
    cfg = cfg or ObservationConfig()

    # Optionally drop / limit exec_trace
    try:
        obs2: Dict[str, Any] = dict(obs)  # shallow copy
        body_obj = obs2.get("observation")
        if isinstance(body_obj, dict):
            active_obj = body_obj.get("active")
            if isinstance(active_obj, dict):
                dsl_obj = active_obj.get("dsl_outputs")
                if isinstance(dsl_obj, dict):
                    # Optionally drop exec_trace
                    if cfg.include_exec_trace is False:
                        if "exec_trace" in dsl_obj:
                            dsl_obj = dict(dsl_obj)
                            dsl_obj.pop("exec_trace", None)
                    else:
                        # limit exec_trace length if configured
                        max_events = cfg.max_trace_events or 500
                        trace_val = dsl_obj.get("exec_trace")
                        if isinstance(trace_val, list) and len(trace_val) > max_events:
                            dsl_obj = dict(dsl_obj)
                            dsl_obj["exec_trace"] = trace_val[:max_events] + ["<truncated>"]
                    # write back
                    active_obj = dict(active_obj)
                    active_obj["dsl_outputs"] = dsl_obj
                    body_obj = dict(body_obj)
                    body_obj["active"] = active_obj
                    obs2["observation"] = body_obj
        obs = obs2  # type: ignore[assignment]
    except Exception:
        # best-effort; if structure differs, ignore
        pass

    return _sanitize(obs, cfg)


def to_yaml(obs: Observation) -> str:
    return yaml.safe_dump(obs, sort_keys=False, allow_unicode=True)

