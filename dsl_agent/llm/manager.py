from typing import Optional
from langchain_core.language_models import BaseLanguageModel
from langchain_openai import ChatOpenAI
import os

DEFAULT_GEMINI_BASE_URL = "https://generativelanguage.googleapis.com/v1beta/openai/"


def create_model(model_name: Optional[str] = None) -> BaseLanguageModel:
    """
    Factory for LLM chat models.
    Controlled by env:
      - LLM_PROVIDER: "gemini_openai" (default) or "openai_compat"
      - MODEL_NAME: fallback if model_name is not passed
      - GEMINI_API_KEY, OPENAI_API_KEY, OPENAI_BASE_URL
    Returns a BaseLanguageModel compatible chat model.
    """
    provider = os.getenv("LLM_PROVIDER", "gemini_openai").lower()
    name = model_name or os.getenv("MODEL_NAME", "gemini-2.5-flash")

    if provider == "openai_compat":
        api_key = os.getenv("OPENAI_API_KEY", "")
        base_url = os.getenv("OPENAI_BASE_URL")  # optional
        return ChatOpenAI(model=name, temperature=0.7, api_key=api_key, base_url=base_url)
    else:
        # Default to Gemini via OpenAI-compatible endpoint
        api_key = os.getenv("GEMINI_API_KEY", "")
        base_url = DEFAULT_GEMINI_BASE_URL
        return ChatOpenAI(model=name, temperature=0.7, api_key=api_key, base_url=base_url)

