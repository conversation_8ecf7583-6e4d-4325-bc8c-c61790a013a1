# Architecture Overview

This document summarizes the high-level architecture, module layout, and dependency directions for the project.

## Layered Architecture (Top → Bottom)

```mermaid
flowchart TB
  subgraph LLM[LLM Context Layer]
    SYS[System YAML Builder]\n(agent/llm/context_builder.py)
    OBS[Observation YAML Builder]\n(agent/llm/observation.py)
  end

  subgraph DSL[DSL / Compiler Layer]
    SCHEMA[DSL Schema Generator]\n(agent/dsl/base.py)
    COMP[Compiler + Visitor]\n(agent/compiler.py, agent/compiler_visitor.py)
  end

  subgraph EXEC[Execution Orchestration Layer]
    GRAPH[LangGraph Builder]\n(agent/graph_builder.py)
    GSTATE[Graph State]\n(agent/graph_state.py)
  end

  subgraph ENV[Environment Layer]
    LINUX[LinuxTerminal]\n(agent/environments/linux_terminal.py)
    DOCKER[DockerTerminal]\n(agent/environments/docker_terminal.py)
    GATK[GATKEnv]\n(agent/environments/gatk_env.py)
  end

  subgraph OBSV[Observation / Telemetry Layer]
    TRACE[exec_trace aggregation]\n(part of GraphState)
  end

  subgraph CONF[Configuration Layer]
    ENVV[Environment Variables]\n(lightweight defaults)
  end

  %% dependencies (strict downward only)
  SYS --> SCHEMA
  OBS --> |reads| CONF
  OBS --> |uses| TRACE
  SCHEMA --> |reads| ENVV
  COMP --> SCHEMA
  GRAPH --> COMP
  GRAPH --> ENV
  GRAPH --> GSTATE
  Agent[Agent Orchestrator]\n(agent/agent.py) --> LLM
  Agent --> EXEC
  Agent --> DSL
  Agent --> OBSV

  style Agent fill:#f9f,stroke:#333,stroke-width:1px
```

- Dependency direction is strictly top → bottom (no upward imports).
- Agent orchestrates planning/compilation/execution, but delegates formatting to the LLM context layer.

## Module Layout (Key Paths)
- agent/llm/: system + observation YAML construction (schema_version included at top level)
- agent/dsl/: DSL schema and utilities, env_tool decorator
- agent/compiler*: DSL → DAG (graph + task_map)
- agent/graph_builder.py: DAG → LangGraph app (exec_trace emitted)
- agent/environments/: runtime environments (LinuxTerminal, DockerTerminal, GATKEnv)
- agent/registry.py: environment registry only (no standalone tools)
- agent/agent.py: outer LangGraph (plan/compile/execute), calls llm/context_builder

## YAML Prompts
- All YAML prompts include a top-level field: `schema_version: 1`
- System YAML: dsl_schema + directives
- Observation YAML: passive/active + dsl_outputs + optional exec_trace (sanitized)

## Principles
- Minimal public interfaces per layer; no cross-layer shortcuts.
- Data → sanitize → YAML; formatting lives in LLM layer only.
- Environments own their methods; no global tool registry.
- exec_trace provides structured, append-only diagnostics.

