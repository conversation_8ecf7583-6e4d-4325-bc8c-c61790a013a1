# Bio Plugin Methods (Quick Reference)

## GATKEnv
- run_gatk(args)
- haplotype_caller(-I bam -R ref -O vcf.gz [...])
- base_recalibrator(-I bam -R ref --known-sites vcf -O table)
- apply_bqsr(-I bam -R ref --bqsr-recal-file table -O bam)
- mark_duplicates(-I bam -O bam --metrics-file metrics)
- genotype_gvcfs(-R ref -V in.g.vcf.gz -O out.vcf.gz [...])
- gather_vcfs(-I list.txt -O out.vcf.gz)
- variant_filtration(-V vcf.gz -O out.vcf.gz --filter-expression ...)
- split_intervals(-R ref -L intervals -O dir [...])
- index_feature_file(-I vcf.gz)
- validate_sam_file(-I bam --MODE SUMMARY|VERBOSE)
- check_output(path) -> "size=<bytes>" | "missing"

## BWAEnv
- index(ref.fa)
- mem_to_sam(ref.fa, R1.fq.gz, R2.fq.gz|None, out.sam, extra_args)
- check_output(path)

## SamtoolsEnv
- view(in.sam, out.bam, extra_args="-bS")
- sort(in.bam, out.bam)
- index(in.bam)
- flagstat(in.bam)
- check_output(path)

## FastQCEnv / MultiQCEnv
- FastQCEnv.run_fastqc(input_fq, outdir="fastqc_out", extra_args="")
- FastQCEnv.check_output(path)
- MultiQCEnv.run_multiqc(input_dir=".", outdir="multiqc_out", extra_args="")

## PicardEnv
- collect_insert_size_metrics(I=input.bam, O=metrics.txt, H=histo.pdf)
- create_sequence_dictionary(R=ref.fa, O=ref.dict)

## BedtoolsEnv
- intersect -a A -b B > out

## BcftoolsEnv
- index vcf.gz
- view vcf.gz [-r region] [> out.vcf.gz]

