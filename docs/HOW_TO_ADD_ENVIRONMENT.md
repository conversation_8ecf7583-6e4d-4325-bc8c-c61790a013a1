# How to Add a New Environment and Methods

This guide shows how to create a new Environment (class) and expose its methods to the DSL.

## 1) Create an Environment Class
- Place it under `agent/environments/your_env.py`
- Inherit from a suitable base (e.g., LinuxTerminal) or build from scratch
- Decorate methods callable from the DSL with `@env_tool`

Example:

```python
# agent/environments/my_env.py
from agent.dsl.base import env_tool

class MyEnv:
    def __init__(self, some_config: str):
        self.some_config = some_config

    @env_tool
    async def do_thing(self, x: int, y: int) -> int:
        return x + y

    @env_tool
    async def close(self) -> None:
        # clean up resources
        pass
```

## 2) Register the Environment
Add it to `agent/registry.py`:

```python
from agent.environments.my_env import MyEnv

ENVIRONMENT_REGISTRY = {
    # ... existing ...
    "MyEnv": MyEnv,
}
```

## 3) Use It in the DSL
- The DSL allows instantiation and method calls:

```python
# plan
my = MyEnv("cfg")
res = my.do_thing(1, 2)
my.close()
```

- During compilation:
  - Instantiation becomes an `instantiation` task
  - Method calls become `method_call` tasks

## 4) Best Practices
- Keep methods narrow and deterministic; return structured results
- Make `close()` idempotent; ensure long-running calls are cancellable/timeboxed (future work)
- Validate inputs early; raise clear errors with actionable messages
- Avoid global state; prefer instance attributes under the environment class

## 5) Troubleshooting
- "Environment class 'X' not found in registry" → check `ENVIRONMENT_REGISTRY`
- AttributeError on method → ensure `@env_tool` exists and method is async
- Large outputs → enable `TRACE_INCLUDE_IO=true` to record summarized I/O in `exec_trace`

