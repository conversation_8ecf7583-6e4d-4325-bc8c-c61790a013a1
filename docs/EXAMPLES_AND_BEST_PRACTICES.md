# Examples and Best Practices

## Minimal DSL Plan

```python
# Instantiate a LinuxTerminal and run a simple command, then close.
sh = LinuxTerminal()
out = sh.run("echo hello")
sh.close()
```

- Produces two tasks and a close:
  - instantiation: `sh = LinuxTerminal()`
  - method_call: `sh.run("echo hello")`
  - method_call: `sh.close()` (the compiler adds dependencies so close runs last)

## Common Pitfalls
- Using a class not in the registry → "Environment class 'X' not found in registry"
- Calling non-async methods → decorate with `@env_tool` and ensure the method is async
- Passing unresolved variables → ensure dependencies exist; undefined vars cause compile errors

## Prompt Construction
- System YAML includes: `schema_version`, directives, and `dsl_schema`
- Observation YAML includes: `schema_version`, passive/active, and `dsl_outputs`
- Observation is sanitized (string/list/dict limits) and can include `exec_trace`

## Debugging Tips
- Set `TRACE_INCLUDE_IO=true` to capture `io_summary` in `exec_trace` for method calls
- Check node-level `status`, `duration_ms`, and `error` in `exec_trace`
- Use smaller prompts first; observe the plan, then iterate

