# System Design Philosophy

## Core Ideas
- Data-first: separate data collection from formatting; YAML is the final rendering, not the source of truth
- Minimal interfaces: each layer exposes only what the upper layer needs
- Determinism when possible: prefer explicit dependencies and idempotent operations
- Observability by default: capture structured `exec_trace`, favor small, meaningful logs
- Environments own tools: no global tool registry; methods are the only tools

## Why YAML everywhere?
- Human-friendly and reviewable
- Works well with snapshots and diffs
- Easy to version (`schema_version`) and evolve during development

## Handling Scale and Complexity
- Keep prompts within budget via sanitization and summaries
- Prefer small, composable environments and methods
- Fail fast at compile time for invalid DSL; produce actionable errors

## Future Directions
- Timeouts, retries, and cancellation policies per node
- Stronger static checks on the DSL (restricted AST)
- Richer telemetry and redaction policies for privacy/security

