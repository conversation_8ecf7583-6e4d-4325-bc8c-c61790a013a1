# Bioinformatics Plugin (WGS/WES/TRS) v0.1

This plugin exposes a minimal set of environments for genomics workflows using the Python DSL:
- FastQCEnv / MultiQCEnv: QC and report aggregation
- BWAEnv: indexing and alignment (SAM output)
- SamtoolsEnv: SAM→BAM conversion, sorting, indexing, stats
- GATKEnv: best-practice tools (HaplotypeCaller, BQSR, mark duplicates, etc.)

All Docker images are pinned or versioned; host `./data` is mounted to `/data`.

## Minimal Examples

### 1) QC and MultiQC
```python
qc = FastQCEnv()
qc.run_fastqc("reads_R1.fastq.gz")
qc.run_fastqc("reads_R2.fastq.gz")
agg = MultiQCEnv()
agg.run_multiqc(".")
```

### 2) WGS-like chain (simplified)
```python
bwa = BWAEnv()
bwa.index("ref.fa")
bwa.mem_to_sam("ref.fa", "R1.fq.gz", "R2.fq.gz", "aln.sam", extra_args="-t 4")

sam = SamtoolsEnv()
sam.view("aln.sam", "aln.bam")
sam.sort("aln.bam", "aln.sorted.bam")
sam.index("aln.sorted.bam")

gatk = GATKEnv()
gatk.base_recalibrator("aln.sorted.bam", "ref.fa", "known_sites.vcf.gz", "recal.table")
gatk.apply_bqsr("aln.sorted.bam", "ref.fa", "recal.table", "aln.bqsr.bam")
gatk.mark_duplicates("aln.bqsr.bam", "aln.dedup.bam", "dup.metrics")
gatk.haplotype_caller("aln.dedup.bam", "ref.fa", "calls.vcf.gz")
```

### 3) WES/TRS note
- Provide intervals via `-L intervals.bed` or interval_list (add via `extra_args`), e.g.
```python
gatk.haplotype_caller("aln.dedup.bam", "ref.fa", "wes_calls.vcf.gz", extra_args="-L exome_intervals.bed")
```

## Best Practices
- Keep environments single responsibility; chain steps explicitly
- Pass absolute/relative paths under `/data`; ensure references and indices exist
- For performance, adjust threads (e.g., `-t N`) via `extra_args`
- Validate outputs (e.g., BAM existence, index files) using `LinuxTerminal.read` or custom checks

## Future Extensions
- Add PicardEnv (CollectMetrics, MarkDuplicates alternative)
- Add bedtools/bcftools/mosdepth environments as needed
- Provide miniature datasets for CI E2E runs (optionally skipped by default)

