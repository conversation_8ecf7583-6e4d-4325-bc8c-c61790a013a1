# Bioinformatics Mini E2E (Placeholder)

This document describes a future minimal E2E workflow using tiny datasets. It is intended to be optionally executed in environments with Docker and test data available.

Plan outline (to be scripted later):
1) Download or place tiny reference (e.g., small human chr snippet) and tiny FASTQ reads into `./data`
2) QC: FastQCEnv.run_fastqc on reads
3) Align: BWAEnv.index + mem_to_sam
4) Convert/Sort/Index: SamtoolsEnv.view/sort/index
5) BQSR + Variant Calling: GATKEnv.base_recalibrator/apply_bqsr/mark_duplicates/haplotype_caller
6) Optional: MultiQC aggregation; Picard metrics; bedtools intersections; bcftools indexing/view

CI Considerations:
- E2E is skipped by default in CI; enable with an env flag and preloaded images/data.
- Each step is executed via DSL plan; outputs verified by file existence checks.

